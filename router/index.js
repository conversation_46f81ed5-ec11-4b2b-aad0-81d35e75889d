/**
 * @file 自定义路由器
 * @date 2020/10/16
 */
import router from '@/router/routers'
import Layout from '@/layout/index'
// 写配置
const customRouter = [
  {
    path: '/checkConfigManage',
    component: Layout,
    children: [
      {
        path: '/checkConfigManage/apiManage/rulerAndNotice/:configCode',
        name: 'RulerAndNotice',
        component: () => import(/* webpackChunkName: "check" */ '_/views/checkConfigManage/apiManage/rulerAndNotice'),
        meta: {
          title: '规则与通知配置',
        }
      }
    ],
    meta: {
      title: '对账接口配置'
    }
  },
  {
    path: '/excessReserveManage',
    component: Layout,
    children: [
      {
        path: '/excessReserveManage/reportDetail/:taskId',
        name: 'reportDetail',
        component: () => import('_/views/excessReserveManage/reportDetail'),
        meta: {
          title: '报备结果详情'
        }
      }
    ],
    meta: {
      title: '备付金管理'
    }
  },
  {
    path: '/costCharging',
    component: Layout,
    children: [
      {
        path: '/costCharging/costRuleConfig/:groupNo',
        name: 'CostRuleConfigDetail',
        component: () => import('_/views/costCharging/costRuleConfig/detail'),
        meta: {
          title: '计费规则详情'
        }
      },
      {
        path: '/costCharging/bossTrialDetail/:trialId',
        name: 'BossTrialDetail',
        component: () => import('_/views/costCharging/bossTrialDetail'),
        meta: {
          title: '试算验收单详情'
        }
      }
    ],
    meta: {
      title: '成本计费管理'
    }
  }
]

router.addRoutes(customRouter)

router.beforeEach((to, from, next) => {
  // 检查即将进入的路由是否需要动态设置标题
  if (to.name === 'RulerAndNotice' && to.params.configCode) {
    // 假设你有一个函数来根据configId动态获取标题
    // console.log('to.params.configId', to)
    // console.log('to.params.configId', to.params.configId)
    to.meta.originTitle = to.meta.originTitle || to.meta.title
    to.meta.title = getDynamicTitle(to.params.configCode, to.meta.originTitle)
  } else if (to.name === 'reportDetail' && to.params.taskId) {
    // 假设你有一个函数来根据configId动态获取标题
    to.meta.originTitle = to.meta.originTitle || to.meta.title
    to.meta.title = getDynamicTitle(to.params.taskId, to.meta.originTitle)
  }
  next()
})

function getDynamicTitle(configCode, title) {
  // 根据configId获取动态标题的逻辑
  return `${ title } - ${ configCode }`
}
export default router
