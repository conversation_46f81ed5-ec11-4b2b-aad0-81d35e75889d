var path = require('path')
var sp = require('shell-spawn')
var utils = require('@yeepay/server-utils')

function parseArgs(key) {
  var args = process.argv
  var foundKey = args.filter(_ => {
    if (_.indexOf('--' + key) !== -1) {
      return true
    }
    return false
  })

  if (foundKey && foundKey.length > 0) {
    var equalIndex = foundKey[0].indexOf('=')
    if (equalIndex !== -1) {
      return foundKey[0].slice(equalIndex + 1)
    }
  }

  return null
}

var module = process.cwd().split('/').slice(-1)[0]
var port = parseArgs('port') || 8013
var commander = 'matriks dev ' + module
if (port) {
  commander += ' -p ' + port
}

utils.info('服务启动中, 请稍后...')

sp(commander, {
  cwd: path.resolve(__dirname, '../../../../'),
  verbose: true
})
