var path = require('path')
var fs = require('fs')
var sp = require('shell-spawn')
var rimraf = require('rimraf')
var utils = require('@yeepay/server-utils')

var module = process.cwd().split('/').slice(-1)[0]
var commander = 'matriks build ' + module
var matriksPath = path.resolve(__dirname, '../../../../')

utils.info('构建中, 请稍后...')

rimraf.sync(path.resolve(__dirname, '../dist'))

sp(commander, {
  cwd: matriksPath
}).then(() => {
  fs.renameSync(matriksPath + '/dist', path.resolve(__dirname, '../dist'))
  utils.done('构建完成, 请部署.')
})
