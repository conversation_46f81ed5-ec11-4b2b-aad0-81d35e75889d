# 运营后台

## 项目介绍

> 业务介绍

### 目录结构

```
|- boss-fund-accounting
    |- api        // http 请求目录
        |- types    // 接口出入参类型存放目录
    |- bin        // 构建脚本
    |- components     // 公共组件
    |- graphql     // graphql 接口
    |- mock        // mock 数据目录
    |- plugins        // 插件
    |- router         // 路由
        |- customRouter  // 详细的路由
        |- index.js
    |- store          // 全局状态管理
    |- styles         // 公共样式
    |- types         // 公共类型存放目录
    |- utils          // 工具存放目录
    |- views          // 页面存放目录
    |- .eslintrc.js    // eslint 规则
    |- .gitignore      // git 忽略文件
    |- .gitlab-ci.yml // gitlab-ci 配置文件
    |- App.vue        // 根组件
    |- babel.config.js // babel 规则
    |- build.sh       // 构建脚本
    |- config.json    // 配置文件
    |- main.ts        // 入口文件
    |- package.json   // 项目依赖
    |- README.md      // 项目 README
    |- tsconfig.json  // ts 规则
    |- vue-shims.d.ts // vue-shims 类型
```

## 如何使用

### 一、安装依赖、启动、构建

```shell
yarn
yarn dev
yarn build
```

## 环境配置
项目放置在 boss-dashboard 的modules 目录下，并切换至v3分支
### 开发代理

文件路径：config.json

示例：

```json
{
  "proxy": {
    "/zjpt-boss": {
      "target": "https://qaboss.yeepay.com",
      "changeOrigin": true
    },
    "/check-center-hessian":{
      "target": "https://qaboss.yeepay.com",
      "changeOrigin": true
    }
  }
}
```

### 请求配置

文件路径：utils/request.js
关键代码：
```javascript
import { serviceFactory } from '@yeepay/client-utils'
// 自定义success code
const codes = ['SUCCESS', '000000', 200]

const baseUrl = process.env.NODE_ENV === 'production' ? '/' : '/'

const service = serviceFactory(baseUrl)

export default service
```

## 生产构建

### 构建脚本

文件路径：build.js

示例：

```javascript
var path = require('path')
var fs = require('fs')
var sp = require('shell-spawn')
var rimraf = require('rimraf')
var utils = require('@yeepay/server-utils')

var module = process.cwd().split('/').slice(-1)[0]
var commander = 'matriks build ' + module
var matriksPath = path.resolve(__dirname, '../../../../')

utils.info('构建中, 请稍后...')

rimraf.sync(path.resolve(__dirname, '../dist'))

sp(commander, {
  cwd: matriksPath
}).then(() => {
  fs.renameSync(matriksPath + '/dist', path.resolve(__dirname, '../dist'))
  utils.done('构建完成, 请部署.')
})
```

## 菜单
运营后台
  资金核算

## 测试环境地址

- 测试环境地址
  - https://qaboss.yeepay.com/boss-fund-accounting/index.html
- 测试账号&密码  ：
  - 自己的账号

- 正式环境地址
  - https://boss.yeepay.com/boss-fund-accounting/index.html


## 业务文档地址
飞书文档地址：[XXX](https://yeepay.feishu.cn/wiki/Yk5xwlHU0i9veUkkoUKcM5HpnEg)

内容每次需求迭代的飞书文档， 开发负责人
------------------------------
需求名称：
开发人员：
需求文档：
接口文档：
------------------------------