import Vue from 'vue'
import init from '@/init'
// import '@/styles/unocss'

import './styles/index.scss'

// mock
// WARNING: `mockjs` NOT SUPPORT `IE` PLEASE DO NOT USE IN `production` ENV.
import './mock'

// @ts-ignore
import App from './App.vue'
import router from './router/index' // custom router
import store from './store/index' // custom store
import { getEnv } from '@/utils/lowcode'
import Render, { config, material } from '@yeepay/lowcode-renderer'
import { material as Antd } from '@yeepay/antd-materials'
import '@yeepay/lowcode-renderer/dist/styles/index.less'
import '@yeepay/antd-materials/dist/styles/index.less'
import '@/assets/styles/index.scss'
import '@/assets/styles/index.less'
import { createService } from '_/utils/request'
Vue.use(Render)
// 安装物料
material.install([Antd].flat())
// 配置信息
config({
  projectCode: 'fundAccountingOperation', // 自动注入项目枚举
  env: getEnv() as ('qa' | 'nc' | 'prod' | undefined),
  i18n: 'zh_CN',
  request: createService('/')
})

init({
  App,
  router,
  store
})
