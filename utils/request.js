import router from '@/router/routers'
import { h } from 'vue'
import { Notification, MessageBox } from 'element-ui'
import { serviceFactory } from '@yeepay/client-utils'
import { downloadFile, copyToClipboard } from '@/utils'
import { relogin } from '@/layout/api/login'

// 自定义success code
const codes = ['SUCCESS', '000000', 200]

function isBlob(data) {
  return Object.prototype.toString.call(data) === '[object Blob]'
}

function notifyError(message) {
  Notification.error({
    message,
    description: ''
  })
}

function copyIcon(text) {
  return h('el-icon-copy', {
    style: {
      cursor: 'pointer',
      marginLeft: '5px'
    },
    on: {
      click: () => {
        copyToClipboard(text)
      }
    }
  })
}

function notifyDetailError(code, traceId, message, url) {
  MessageBox({
    title: '操作失败',
    type: 'error',
    dangerouslyUseHTMLString: true,
    message: `<div>
     <div style="margin-bottom: 5px;">TraceId: ${traceId || ''}</div>
     <div style="margin-bottom: 5px;">错误编码: ${code}</div>
     <div style="margin-bottom: 5px;">错误原因: ${message}</div>
    </div>`,
    customClass: 'error-tips-info',
    showCancelButton: true,
    cancelButtonText: '复制错误信息',
    confirmButtonText: '我知道了',
    beforeClose: (action, instance, done) => {
      if (action === 'cancel') {
        copyToClipboard(`我遇到问题了，下面是系统错误信息，请帮我看看～

      请求地址：${url}

      TraceId: ${traceId || ''}
      操作结果: 失败
      错误码: ${code}
      错误信息: ${message}`)

        done()
      } else {
        done()
      }
    }
  })
}

function successCallback(response) {
  // [特殊处理] 若为文件流, 则直接下载该文件
  if (response.data.code === undefined || response.data.code === null) {
    const disposition = response.headers['content-disposition']
    if (disposition) {
      const fileinfo = disposition.split(';')[1]
      const filename = decodeURIComponent(fileinfo.split('=')[1])
      downloadFile(response.data, filename.split('.')[0], filename.split('.')[1])
    } else {
      if (isBlob(response.data)) {
        var reader = new FileReader()
        reader.readAsText(response.data, 'utf-8')
        reader.onload = function() {
          // 读取完毕后输出结果
          const resultObj = JSON.parse(this.result)
          codes.includes(resultObj.code) || notifyDetailError(resultObj.code, resultObj.traceId, resultObj.message || resultObj.msg, response.config.url)
        }
      }
    }
  }
}

function failCallback(response) {
  if (response.data.code === 400) {
    relogin()
    return
  }

  // 权限中心的历史遗留问题, 401 常规应为未认证, 但是权限中心 401 作为未授权
  if (response.data.code === 401) {
    notifyError(response.data.message || response.data.msg)
    return
  }

  // 权限中心的历史遗留问题, 403 常规应为未授权, 但是权限中心 403 作为未认证
  if (response.data.code === 403) {
    notifyError(response.data.message || response.data.msg)
    // 未登录
    const payload = response.data.data
    if (payload && payload.login_url) {
      window.location.href = payload.login_url + '?callback=' + window.location.href.split('#')[0]
    }

    return
  }

  notifyDetailError(response.data.code, response.data.traceId, response.data.message || response.data.msg, response.config.url)
}

function unauthorizedCallback(response) {
  Notification.error({
    title: '未认证'
  })
  router.push({ path: '/401' })
}

function forbiddenCallback(response) {
  Notification.error({
    title: '禁止访问'
  })
  router.push({ path: '/403' })
}

function notfoundCallback(response) {
  Notification.error({
    title: '接口未找到'
  })
}

export function createService(baseUrl, code) {
  const options = {
    baseUrl,
    code,
  }
  return serviceFactory(
    options,
    successCallback,
    failCallback,
    unauthorizedCallback,
    forbiddenCallback,
    notfoundCallback
  )
}

const baseUrl = process.env.NODE_ENV === 'production' ? '/' : '/'
// const timeout = 5 * 60 * 1000
const service = createService(baseUrl, codes)

export default service
