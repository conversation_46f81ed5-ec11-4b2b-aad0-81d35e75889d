async function fetchEnum({ qaCode, code }) {
  const origin = window.location.origin
  const env = window.localStorage.getItem('__yeepay_lowcode_environment__')
  let jsonUrl = ''
  if (origin === 'https://boss.yeepay.com' || origin === 'https://ncsrs.yeepay.com') {
    if (env === 'nc') {
      jsonUrl = `https://img.yeepay.com/pmc-static/pmc/enumeration/${code}-SNAPSHOT.json`
    } else {
      jsonUrl = `https://img.yeepay.com/pmc-static/pmc/enumeration/${code}-RELEASE.json`
    }
  } else {
    jsonUrl = `https://qastaticres.yeepay.com/ptyfb-fmc-static/pmc/enumeration/${qaCode}-RELEASE.json`
  }

  return fetch(jsonUrl, {
    cache: 'no-cache'
  }).then((response) => {
    return response.text().then((data) => {
      if (data) {
        return fetch(data).then((res) => {
          return res.json().then((resData) => {
            return resData.reduce((obj, item) => {
              obj[item.enumeration] = item.values.map((v) => {
                return { label: v.value, value: v.key }
              })
              return obj
            }, {})
          })
        })
      }
    })
  })
}

export {
  fetchEnum
}
