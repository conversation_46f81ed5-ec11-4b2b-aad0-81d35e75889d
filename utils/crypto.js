// sqlStr:crypto.sm2.encrypt(o.sqlStr, variables.sm2KeyPair.publicKey)||''
// {{ {
//   publicKey:'04e6eebd16cfda42007c6a21a439e878794fa18a72f8fb6125ffe77838aa8e287a8b9af0bb90981fdaa00f47c8f01a06e60d602e60151e3047144584dc01a9f9f8',
//   privateKey:'b7e0a8cac2230ca436ee7299fa5222a1e960c2cc6116d730462500fb38763b05'
//   }}}
const publicKey = '04e6eebd16cfda42007c6a21a439e878794fa18a72f8fb6125ffe77838aa8e287a8b9af0bb90981fdaa00f47c8f01a06e60d602e60151e3047144584dc01a9f9f8'
const privateKey = 'b7e0a8cac2230ca436ee7299fa5222a1e960c2cc6116d730462500fb38763b05'

const sm2 = require('sm-crypto').sm2

// sm2加密
export const sm2Encrypt = (str) => {
  return sm2.doEncrypt(str, publicKey)
}

// sm2解密
export const sm2Decrypt = (encryptedData) => {
  return sm2.doDecrypt(encryptedData, privateKey)
}
