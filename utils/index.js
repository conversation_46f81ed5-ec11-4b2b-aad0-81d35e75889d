import { uid } from 'radash'

/**
 * method map to list<key, value>.
 * @param {Object} mapObj.
 * @return {Array} return Array list.
 */
function mapToList(mapObj) {
  return Object.keys(mapObj).map(key => ({
    key,
    value: mapObj[key]
  }))
}
/**
 * 读取文件并将其转换为Base64编码字符串
 * @param {File} file - 要读取的文件
 * @returns {Promise} - 当文件读取完成时解决，并返回Base64编码字符串。如果发生错误，则拒绝并返回错误对象
 */
function getBase64(file) {
  return new Promise((resolve, reject) => {
    const reader = new FileReader()
    reader.readAsDataURL(file)
    reader.onload = () => resolve(reader.result)
    reader.onerror = error => reject(error)
  })
}
// 金额转千分位显示
function toThousands(num, precision = 2) {
  // 将数字转换为字符串
  const [integerPart, decimalPart] = Number(num || 0).toFixed(precision).split('.')

  // 对整数部分进行千分位处理
  const formattedIntegerPart = integerPart.replace(/\B(?=(\d{3})+(?!\d))/g, ',')

  // 拼接整数部分和小数部分
  return decimalPart ? `${formattedIntegerPart}.${decimalPart}` : formattedIntegerPart
}

/**
 * 将给定的字符串缩短为指定的最大长度，并在中间添加省略号（...）
 * 如果字符串本身长度小于或等于最大长度，则直接返回原字符串
 * @param {string} str - 要缩短的字符串
 * @param {number} maxLength - 最大允许长度
 * @return {string} 缩短后的字符串
 */
function shortenString(str, maxLength = 10) {
  // 如果不是字符串，直接返回原值
  if (typeof str !== 'string') {
    return str
  }

  // 如果字符串长度小于等于最大长度，直接返回原字符串
  if (str.length <= maxLength) {
    return str
  }

  // 计算两端各保留的字符数量
  // 减去3是因为我们要在中间加上的"..."占用3个字符位置
  const charsToShow = maxLength - 3
  const frontChars = Math.ceil(charsToShow / 2)
  const backChars = Math.floor(charsToShow / 2)

  // 构造并返回新字符串
  return str.substr(0, frontChars) + '...' + str.substr(str.length - backChars)
}

/**
 * @description: 从枚举中获取label
 * @param {*} value 值
 * @param {*} enums 目标枚举
 * @param {*} mapper label和value的映射关系，默认['label', 'value']
 * @returns label, 未找到返回value
 */
function getLabelFromEnum(value, enums = [], mapper = ['label', 'value']) {
  if (!Array.isArray(enums)) return value
  const target = enums.find(item => item[mapper[1]] === value)
  return target ? target[mapper[0]] : value
}
/**
 * @description 获取一个uid,内置长度为7
 * @param specialKey 特殊key
 * @returns {string} uid
 */
function getUid(specialKey = '') {
  return uid(7, specialKey)
}

export {
  mapToList,
  getBase64,
  toThousands,
  getLabelFromEnum,
  getUid,
  shortenString
}
