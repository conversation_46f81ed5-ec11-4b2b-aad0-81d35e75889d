/**
 * @file 注册 gql 语句
 * <AUTHOR> FE
 * @date 2020/11/17
 * @description ❗️请遵循 /module/xxx.graphql 创建 gql
 */

import { apolloClient } from './index'
const inject = apolloClient.inject

const gqls = require.context('./', true, /\.graphql$/)

gqls.keys().reduce((modules, path) => {
  const moduleName = path.replace(/^\.\/(.*)\/(.*)\.\w+$/, '$1')
  const value = gqls(path)
  inject(moduleName, value)
  return modules
}, {})
