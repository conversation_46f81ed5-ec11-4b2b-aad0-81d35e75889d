<script lang="ts">
import { defineComponent } from '@vue/composition-api'
import { getConfigInfoApi, saveRuleApi } from '_/api/checkInterfaceManage'
import { ConfigRule, RuleDetaillVo } from '_/types/interFace'
import { rulerColumns } from '../defines/column'
import MonacoEditor from '@/components/MonacoEditor/index.vue'
import { SUPPORTED_LANGUAGES } from '@/components/MonacoEditor/config'
import { sm2Encrypt } from '_/utils/crypto'
import * as _ from 'radash'
import emmitter from '@/utils/event-emitter'
import eventBus from '_/utils/eventBus'

// 获取 SUPPORTED_LANGUAGES 值的联合类型
type EditorLanguage = typeof SUPPORTED_LANGUAGES[keyof typeof SUPPORTED_LANGUAGES]

export default defineComponent({
  name: 'RulerAndNotice',
  components: {
    MonacoEditor
  },
  data() {
    return {
      schema: {} as any,
      otherForm: {
        otherConfigId: ''
      },
      // 导入表单校验
      otherFormRules: {
        otherConfigId: [
          {
            validator: (rule: any, value: any, callback: any) => {
              if (!value) {
                callback(new Error('请输入对账编码'))
              }
              if (value === this.configCode) {
                callback(new Error('不允许导入当前对账编码'))
              }
              callback()
            },
            trigger: 'change'
          },
        ]
      },
      rulerFormRules: {
        configRuleName: [
          { required: true, message: '请输入规则组名称', trigger: 'change' }
        ],
        notifyWay: [
          { required: true, message: '请选择通知方式', trigger: 'change' }
        ]
      },
      title: this.$route.meta.title,
      configCode: this.$route.params.configCode,
      // 规则配置数组
      rulerList: [] as ConfigRule[],
      // 原始规则配置数组
      rulerOriginList: [] as ConfigRule[],
      // 规则配置加载中
      rulerLoading: false,
      // 保存规则加载中
      saveLoading: false,
      // 通知方式
      notifyWayListOptions: [] as any[],
      // 配置类型
      configTypeEnum: [] as any[],
      // 配置表格表格列配置
      columns: rulerColumns,

      // 编辑弹窗
      editVisiavle: false,
      // 编辑器内容
      editorContent: ``,
      editorLanguage: 'text' as EditorLanguage,
      /** 当前窗口操作信息 */
      modify_rIndex: -1,
      modify_index: -1,
      modify_column: '' as keyof RuleDetaillVo,
      modify_value: '',
    }
  },
  computed: {
    // 获取规则配置
    rulerEmpty() {
      return this.rulerList.length === 0
    },
    terseRoute() {
      let route = null
      if (this.$route.matched.length >= 3) {
        route = this.$route.matched[1]
      } else {
        route = this.$route
      }
      return route
    }
  },

  async mounted() {
    this.getRulerFromConfigId(this.configCode)
    eventBus.on('RULER:OPEN', (configCode: string) => {
      if (configCode === this.configCode) {
        this.getRulerFromConfigId(this.configCode)
        this.otherForm.otherConfigId = ''
      }
    })
    this.notifyWayListOptions = JSON.parse(localStorage.getItem('notifyWayEnum') || '[]').map((item: any) => {
      return {
        label: item.value,
        value: item.key
      }
    })
    this.configTypeEnum = JSON.parse(localStorage.getItem('configTypeEnum') || '[]').map((item: any) => {
      return {
        label: item.value,
        value: item.key
      }
    })
  },

  async activated() {

  },
  methods: {
    initEditorModal() {
      this.modify_rIndex = -1
      this.modify_index = -1
      this.modify_column = '' as keyof RuleDetaillVo
      this.modify_value = ''
    },
    closeEditorModal() {
      this.editorContent = ''
      this.editVisiavle = false
      this.initEditorModal()
    },
    // 导入其他接口规则
    importOtherInterFaceRuler() {
      // 校验编码
      if (this.$refs.otherFormRef) {
        // 归一化为数组
        const formRefs = Array.isArray(this.$refs.otherFormRef) ? this.$refs.otherFormRef : [this.$refs.otherFormRef]
        // 所有的ref验证通过才能执行导入逻辑
        const allValid = formRefs.every((form: any) => {
          let curValid = false
          form.validate((valid: any) => {
            curValid = valid
          })
          return curValid
        })
        if (allValid) {
          this.getRulerFromConfigId(this.otherForm.otherConfigId, false)
        }
      }
    },

    /**
     * 获取规则配置
     * @param configCode 接口配置code
     * @param isImport 是否是导入动作
     * @returns 规则配置数组
     */
    getRulerFromConfigId(configCode: string, isImport: boolean = true) {
      this.rulerLoading = true
      getConfigInfoApi({
        configCode: configCode,
      }).then((res) => {
        const { code, data, message } = res
        if (code === 200) {
          this.rulerOriginList = JSON.parse(JSON.stringify(data.rulelVos))
          this.rulerList = data.rulelVos.map((item: ConfigRule) => {
            item.ruleDetaillVoList = item.ruleDetaillVoList.map((detail: RuleDetaillVo) => {
              return {
                ...detail,
                uniqueKey: _.uid(10),
              }
            })
            return {
              ...item,
              appName: isImport ? item.appName : this.configCode,
              isEdit: isImport // 默认是编辑动作，历史数据不可编辑
            }
          })
        } else {
          this.rulerList = []
          this.$message.error(message)
        }
      }).finally(() => {
        this.rulerLoading = false
        return this.rulerList
      })
    },

    /**
     * @description 新增规则
     */
    handleAddRuler() {
      const ruler: ConfigRule = {
        isEdit: false, // 标记为不是编辑状态
        configCode: '',
        configRuleName: '',
        notifyWay: '',
        notifyWayDesc: '',
        appName: '',
        fileJobId: '',
        ruleDetaillVoList: [] as RuleDetaillVo[]
      }
      this.rulerList.push(ruler)
    },

    /**
     * @description 规则配置修改
     * @param rIndex 规则索引
     * @param index 配置索引
     * @param column 配置字段
     * @param value 值
     */
    onCellChange(rIndex: number, index: number, column: keyof RuleDetaillVo, value: any) {
      this.$nextTick(() => {
        this.rulerList[rIndex].ruleDetaillVoList[index][column] = value
      })
    },

    /**
     * @description 打开单元格编辑
     * @param rIndex 规则索引
     * @param index 配置索引
     * @param column 配置字段
     * @param value 值
     * @param type 类型
     */
    onOpenCell(rIndex: number, index: number, column: keyof RuleDetaillVo, value: any) {
      let type = SUPPORTED_LANGUAGES.TEXT
      if (['sqlStr'].includes(String(column))) {
        type = SUPPORTED_LANGUAGES.SQL
      } else if (['outputColumn'].includes(String(column))) {
        type = SUPPORTED_LANGUAGES.JSON
      }

      this.$nextTick(() => {
        // 给编辑器设置默认值
        this.editorContent = value
        type && (this.editorLanguage = type)
        // 保存要修改的值的定位信息
        this.modify_rIndex = rIndex
        this.modify_index = index
        this.modify_column = column
        this.modify_value = value
        this.editVisiavle = true
      })
    },

    /**
     * @description 添加配置
     * @param index 配置索引
    */
    addConfig(index: number) {
      // 新增一条配置
      // 如果最新一条配置不为空，则新增一条配置
      if (this.rulerList[index].ruleDetaillVoList.length > 0) {
        const lastConfig = this.rulerList[index].ruleDetaillVoList[this.rulerList[index].ruleDetaillVoList.length - 1]
        if (lastConfig.ruleType === '' && lastConfig.datasourceConfig === '' && lastConfig.errorType === '' && lastConfig.sqlStr === '' && lastConfig.outputColumn === '' && lastConfig.outputTableName === '') {
          return
        }
      }
      this.rulerList[index].ruleDetaillVoList.push({
        uniqueKey: _.uid(10),
        ruleType: '',
        datasourceConfig: '',
        errorType: '',
        sqlStr: '',
        outputColumn: '',
        outputTableName: '',
      } as RuleDetaillVo)
    },

    /**
     * @description 删除配置
     * @param rulerIndex 规则索引
     * @param index 配置索引
     */
    delConfig(rulerIndex: number, index: number) {
      const dataSource = [...this.rulerList[rulerIndex].ruleDetaillVoList]
      dataSource.splice(index, 1)
      this.rulerList[rulerIndex].ruleDetaillVoList = dataSource
    },

    /**
     * @description 编辑器内容改变
     * @param value 内容
     */
    onEditorChange(value: string) {
      this.editorContent = value || ''
    },

    /** 处理编辑弹窗 */
    handleEditorModal() {
      this.onCellChange(this.modify_rIndex, this.modify_index, this.modify_column, this.editorContent)
      this.closeEditorModal()
    },

    /**
     * @description 删除规则
     * @param index 规则索引
     */
    delRuler(index: number) {
      this.rulerList.splice(index, 1)
    },

    /**
     * @description 验证所有规则表单
     * @returns Promise<boolean> 是否全部验证通过
     */
    async validateRulerForms(): Promise<boolean> {
      try {
        const formRefs = this.$refs.rulerForm
        if (!formRefs || !formRefs.length) {
          return false
        }

        // 执行所有表单的校验
        const validateResults = await Promise.all(
          formRefs.map((form: any) => {
            return new Promise((resolve) => {
              form.validate((valid: boolean) => {
                resolve(valid)
              })
            })
          })
        )

        // 检查是否所有表单都验证通过
        return validateResults.every(valid => valid)
      } catch (error) {
        this.$message.error('表单验证出错')
        return false
      }
    },

    /**
     *  @description 处理请求参数
     */
    handleSaveRequestParams() {
      // 规则组名称不能相同
      let hasSameConfigRuleName = false
      const configRuleNameSet = new Set()
      this.rulerList.forEach((ruler) => {
        if (configRuleNameSet.has(ruler.configRuleName)) {
          hasSameConfigRuleName = true
        }
        configRuleNameSet.add(ruler.configRuleName)
      })
      if (hasSameConfigRuleName) {
        this.$message.error('规则组名称不能相同')
        return
      }
      const requestParams = this.rulerList.map((ruler) => {
        return {
          ...ruler,
          ruleDetaillVoList: ruler.ruleDetaillVoList.map((detail: RuleDetaillVo) => {
            const _detail = {} as RuleDetaillVo
            _detail.datasourceConfig = detail.datasourceConfig
            _detail.errorType = detail.errorType
            _detail.outputColumn = detail.outputColumn
            _detail.outputTableName = detail.outputTableName
            _detail.sqlStr = sm2Encrypt(detail.sqlStr || '')
            _detail.ruleType = detail.ruleType
            // 如果有id，则传递id
            if (detail.id) {
              _detail.id = detail.id
            }
            return _detail
          })
        }
      })
      return {
        configCode: this.configCode,
        rulelVos: requestParams
      }
    },

    /**
     * @description 保存规则
     */
    saveRuler() {
      const requestParams = this.handleSaveRequestParams()
      requestParams && saveRuleApi(requestParams).then((res) => {
        if (res.code === 200) {
          // 重新获取规则
          this.getRulerFromConfigId(this.configCode)
          this.$message.success('保存成功')
        } else {
          this.$message.error(res.message)
        }
      }).finally(() => {
        this.saveLoading = false
      })
    },

    /**
     * @description 保存
     */
    async handleSave() {
      this.saveLoading = true
      const isValid = await this.validateRulerForms()
      if (isValid) {
        // 所有表单验证通过，执行保存逻辑
        this.saveRuler()
      } else {
        this.$message.error('请完善表单信息')
        this.saveLoading = false
      }
    },
    /**
     * @description 关闭当前页面
     */
    handleClose() {
      // 页面关闭
      emmitter.emit('VIEW:CLOSE', this.$route)
    }
  }
})
</script>
<template>
  <div class="p-5 config-container">
    <div class="title">
      <h3>{{ title }}</h3>
    </div>
    <a-divider/>
    <!-- 复制表单 -->
    <div class="copy">
      <a-form-model
        ref="otherFormRef"
        layout="inline"
        :model="otherForm"
        :rules="otherFormRules"
      >
        <a-row :gutter="16">
          <a-col :xs="20" :sm="10">
            <a-form-model-item
              label="导入对账编码"
              labelAlign="right"
              required
              :labelCol="{
                span: 7
              }"
              :wrapperCol="{
                span: 17
              }"
              extra="必填，不允许输入当前对账编码"
              style="width: 100%;"
              prop="otherConfigId"
            >
              <a-input
                v-model="otherForm.otherConfigId"
                placeholder="请输入要导入的对账编码"
              />
            </a-form-model-item>
          </a-col>
          <a-col :span="2">
            <a-form-model-item
            >
              <a-button type="primary" @click="importOtherInterFaceRuler">
                导入
              </a-button>
            </a-form-model-item>
          </a-col>
        </a-row>
      </a-form-model>
    </div>
    <a-divider/>
    <!-- 规则配置 -->
    <a-spin :spinning="rulerLoading || saveLoading">
      <div class="ruler-container">
        <div class="ruler-header">
          <h3>规则配置</h3>
          <a-button type="primary" icon="plus" @click="handleAddRuler">新增规则</a-button>
        </div>
        <a-divider/>
        <a-empty v-if="rulerEmpty"/>
        <div v-else class="ruler-content">
          <!-- 遍历规则 -->
          <div v-for="(ruler,i) in rulerList" :key="i" class="ruler-item" >
            <!-- 规则组主要信息 -->
            <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 16px;">
              <h3>规则 {{ i + 1 }}</h3>
              <a-popconfirm
                title="确定要删除该规则吗?"
                ok-text="确定"
                cancel-text="取消"
                @confirm="() => delRuler(i)"
              >
                <a-button type="link">删除规则</a-button>
              </a-popconfirm>
            </div>
            <div class="ruler-main">
              <a-form-model
                :ref="`rulerForm`"
                layout="horizontal"
                :label-col="{span:8}"
                :wrapper-col="{span:16}"
                :model="ruler"
                :rules="rulerFormRules"
              >
                <a-row :gutter="[16,16]">
                  <a-col :lg="12">
                    <a-form-model-item
                      class="ruler-item-name"
                      label="规则组名称"
                      prop="configRuleName"
                      required
                    >
                      <span v-if="ruler.isEdit">{{ ruler.configRuleName }}</span>
                      <a-input v-else v-model="ruler.configRuleName"></a-input>
                    </a-form-model-item>
                  </a-col>
                  <a-col :lg="12">
                    <a-form-model-item
                      class="ruler-item-noticeway"
                      label="单边通知方式"
                      prop="notifyWay"
                      required
                    >
                      <a-select v-model="ruler.notifyWay" :options="notifyWayListOptions"></a-select>
                    </a-form-model-item>
                  </a-col>
                  <a-col :lg="12">
                    <a-form-model-item
                      class="ruler-item-noticeway"
                      label="业务系统名称"
                    >
                      <a-input v-model="ruler.appName"></a-input>
                    </a-form-model-item>
                  </a-col>
                  <a-col :lg="12">
                    <a-form-model-item
                      class="ruler-item-noticeway"
                      label="文件解析作业编码"
                    >
                      <a-input v-model="ruler.fileJobId"></a-input>
                    </a-form-model-item>
                  </a-col>
                </a-row>
              </a-form-model>
            </div>
            <a-divider style="margin-bottom: 12px;"/>
            <!-- 规则组规则明细 -->
            <div class="ruler-detail">
              <a-table
                :columns="columns"
                :data-source="ruler.ruleDetaillVoList"
                size="small"
                :pagination="false"
                :row-key="'uniqueKey'"
              >
                <template slot="ruleType" slot-scope="text,record,index">
                  <div>
                    <a-select
                      placeholder="请选择"
                      allow-clear
                      :value="text"
                      style="width: 100%"
                      :options="configTypeEnum"
                      @change="value => onCellChange(i , index, 'ruleType', value)">
                    </a-select>
                  </div>
                </template>
                <template v-for="column in ['datasourceConfig', 'errorType', 'outputTableName', 'outputColumn', 'sqlStr']" :slot="column" slot-scope="text,record,index">
                  <div :key="column" class="edit-cell">
                    <a-input
                      :value="text"
                      placeholder="请输入"
                      allow-clear
                      @change="e => onCellChange(i , index, column, e.target.value)"/>
                    <a-icon
                      type="edit"
                      class="edit-cell-icon"
                      @click="() => onOpenCell(i , index, column, text)"/>
                  </div>
                </template>
                <template slot="action" slot-scope="text,record,index">
                  <a-popconfirm title="确认删除吗?" @confirm="() => delConfig(i, index)">
                    <a href="javascript:;">删除</a>
                  </a-popconfirm>
                </template>
                <template slot="footer">
                  <div>
                    <a-button type="dashed" :block="true" icon="plus" style="opacity: 0.8" @click="() => addConfig(i)">新增</a-button>
                  </div>
                </template>
              </a-table>
            </div>
          </div>
        </div>

        <a-modal v-model="editVisiavle" :width="'800px'" title="编辑" @ok="handleEditorModal">
          <MonacoEditor :uniqueKey="'rulerEditor'" :initValue="editorContent" :language="editorLanguage" :theme="'vs-black'" @change="onEditorChange"></MonacoEditor>
        </a-modal>
      </div>
      <div class="operator-container">
        <a-button @click="handleClose">取消</a-button>
        <a-button type="primary" @click="handleSave">保存</a-button>
      </div>
    </a-spin>

  </div>
</template>

<style lang="less" scoped>
.ruler-container {
  display: flex;
  flex-direction: column;
  gap: 16px;
  background-color: #f7f7f7;
  padding: 16px;
  margin-bottom: 48px;
  .ruler-header{
    display: flex;
    justify-content: space-between;
    align-items: center;
  }
  h2{
    margin: 0;
  }
  .ant-divider-horizontal{
    margin: 0;
  }
}
.ruler-content{
  display: flex;
  flex-direction: column;
  gap: 16px;
  .ruler-item{
    background-color: white;
    padding: 16px;
  }
}
// 移除表单项的margin-bottom
.ant-form-item{
  margin-bottom: 0;
}

.edit-cell{
  display: flex;
  align-items: center;
  justify-content: center;
  .edit-cell-icon{
    margin-left: 8px;
  }
}
.operator-container {
  position: fixed;
  bottom: 0;
  right: 0;
  width: 100%;
  height: 48px;
  padding: 10px 16px;
  background: #fff;
  text-align: right;
  border-top: 1px solid #e9e9e9;
  z-index: 999;
  display: flex;
  justify-content: flex-end;
  gap: 16px;
}
</style>
