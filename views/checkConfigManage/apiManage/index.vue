<script lang="ts">
import { defineComponent } from '@vue/composition-api'
import { fetchSchema } from '@/utils/lowcode'
import eventBus from '_/utils/eventBus'
export default defineComponent({
  data() {
    return {
      pageCode: 'checkApiManage',
      schema: {} as any
    }
  },
  async mounted() {
    this.schema = await fetchSchema(this.pageCode)
  },
  methods: {
    routeTo(path: string) {
      if (path.includes('/checkConfigManage/apiManage/rulerAndNotice')) {
        const visitedViews = this.$store.state.tagsView.visitedViews
        const isExist = visitedViews.find((item: any) => path === item.fullPath)
        if (!isExist) {
          // 从path中获取configCode
          // 先移除?参数
          const configCode = path.split('?')[0].split('/').pop()
          eventBus.emit('RULER:OPEN', configCode)
        }
      }
      this.$router.push(path)
    }
  }
})
</script>
<template>
  <div class="p-5">
    <low-code-renderer v-if="schema.components" :schema="schema" @routeTo="routeTo"/>
  </div>
</template>
