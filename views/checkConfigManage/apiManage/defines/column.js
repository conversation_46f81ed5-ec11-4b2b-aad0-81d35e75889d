export const rulerColumns = [
  {
    title: '配置类型',
    dataIndex: 'ruleType',
    width: 120,
    key: 'ruleType',
    scopedSlots: { customRender: 'ruleType' }
  },
  {
    title: '数据源',
    dataIndex: 'datasourceConfig',
    width: 120,
    key: 'datasourceConfig',
    scopedSlots: { customRender: 'datasourceConfig' }
  },
  {
    title: '差错类型',
    dataIndex: 'errorType',
    width: 120,
    key: 'errorType',
    scopedSlots: { customRender: 'errorType' }
  },
  {
    title: '输出映射关系',
    dataIndex: 'outputColumn',
    width: 120,
    key: 'outputColumn',
    scopedSlots: { customRender: 'outputColumn' }
  },
  {
    title: '输出数据表名',
    dataIndex: 'outputTableName',
    width: 120,
    key: 'outputTableName',
    scopedSlots: { customRender: 'outputTableName' }
  },
  {
    title: 'SQL',
    dataIndex: 'sqlStr',
    width: 120,
    key: 'sqlStr',
    scopedSlots: { customRender: 'sqlStr' }
  },
  {
    title: '操作',
    dataIndex: 'action',
    width: 60,
    key: 'center',
    scopedSlots: { customRender: 'action' }
  },
]
