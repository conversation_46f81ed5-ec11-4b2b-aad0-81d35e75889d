<script lang="ts">
import { defineComponent } from '@vue/composition-api'
import { fetchSchema } from '@/utils/lowcode'
export default defineComponent({
  name: 'BossCostOrder',
  data() {
    return {
      pageCode: 'bossCostOrder',
      schema: {} as any
    }
  },
  async mounted() {
    this.schema = await fetchSchema(this.pageCode)
  },
  methods: {
    routeTo(path: string) {
      this.$router.push(path)
    }
  }
})
</script>
<template>
  <div class="p-5">
    <low-code-renderer v-if="schema.components" :schema="schema" @routeTo="routeTo"/>
  </div>
</template>

<style lang="less" scoped>

</style>
