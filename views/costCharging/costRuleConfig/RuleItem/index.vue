<template>
  <div class="ruler-item-container">
    <a-table
      :rowKey="rowKey"
      :dataSource="dataSource"
      :columns="columns"
      :pagination="pagination"
      :size="size"
      :show-header="showHeader"
      :scroll="{ x: dynamicChirldWidth }"
      :bordered="bordered"
    >
      <template v-for="specialKey,_index in specialKeys" :slot="specialKey" slot-scope="text, record, index">
        <!-- (specialKey + '__' + index) 想表达的是 第几行的那一列 -->
        <!-- 选项列 -->
        <a-select
          v-if="record.hasOwnProperty(specialKey + '__options')"
          :key="specialKey + '__' + index"
          :class="record[specialKey+'__err_msg']?'err':''"
          :value="text"
          :getPopupContainer="triggerNode => triggerNode.parentNode"
          :options="record[specialKey + '__options']"
          @change="(value) => onSelectChange(index, specialKey, value)"
        >
        </a-select>

        <!-- 对象类型列 -->
        <div
          v-if="record.hasOwnProperty(specialKey + '__object')"
          :key="specialKey + '__' + index"
        >
          <a-row class="w-full">
            <a-col v-for="ele in record[specialKey+'__object'].elements" :key="specialKey + '__' + ele.dataIndex" :span="ele.span">
              <a-form-item :label="ele.title" v-bind="ele.layout" :required="ele.required">
                <!-- 输入框 -->
                <a-input
                  v-if="ele.type === 'input'"
                  :class="record[specialKey][ele.dataIndex+'__err_msg']?'err':''"
                  :value="record[specialKey][ele.dataIndex]"
                  :max-length="ele.maxLength"
                  @change="(value)=>{onCellChange(index, specialKey, value.target.value, !!(rules[ele.dataIndex]&&rules[ele.dataIndex].validator), options={valueType: 'object', objectConfig: { field: ele.dataIndex }})}"
                />
                <!-- 下拉框 -->
                <a-select
                  v-if="ele.type === 'select'"
                  :class="record[specialKey][ele.dataIndex+'__err_msg']?'err':''"
                  :value="record[specialKey][ele.dataIndex]"
                  :allow-clear="ele.allowClear"
                  :options="ele.options"
                  @change="(value)=>{onCellChange(index, specialKey, value, !!(rules[ele.dataIndex]&&rules[ele.dataIndex].validator), options={valueType: 'object', objectConfig: { field: ele.dataIndex }})}"
                />
                <!-- 数字输入框 -->
                <a-input-number
                  v-show="ele.isShow ? ele.isShow(index) : true"
                  v-if="ele.type === 'number'"
                  :class="record[specialKey][ele.dataIndex+'__err_msg']?'err':''"
                  :value="record[specialKey][ele.dataIndex]"
                  :min="Number.isNaN(ele.min) ? -Infinity : ele.min"
                  :max="Number.isNaN(ele.max) ? Infinity : ele.max"
                  :placeholder="ele.placeholder"
                  :step="Number.isNaN(ele.step) ? undefined : ele.step"
                  :precision="Number.isNaN(ele.precision) ? undefined : ele.precision"
                  @change="(value)=>{onCellChange(index, specialKey, value, !!(rules[ele.dataIndex]&&rules[ele.dataIndex].validator), options={valueType: 'object', objectConfig: { field: ele.dataIndex }})}"
                />
                <!-- 单选 -->
                <a-radio-group
                  v-if="ele.type === 'radio'"
                  :class="record[specialKey][ele.dataIndex+'__err_msg']?'err':''"
                  :value="record[specialKey][ele.dataIndex]"
                  :options="ele.options"
                  @change="(e)=>{onCellChange(index, specialKey, e.target.value, !!(rules[ele.dataIndex]&&rules[ele.dataIndex].validator), options={valueType: 'object', objectConfig: { field: ele.dataIndex }})}"
                >
                </a-radio-group>

                <!-- 错误信息 -->
                <div v-show="record[specialKey][ele.dataIndex+'__err_msg']" :key="specialKey+'__err_info'" class="err-msg">
                  {{ record[specialKey][ele.dataIndex+'__err_msg'] }}
                </div>
              </a-form-item>
            </a-col>
          </a-row>

        </div>
        <!-- 兜底，直接展示列 -->
        <div v-else :key="specialKey + '__' + index">
          <!-- 不需要转义的列 -->
          <span v-if="!record.hasOwnProperty(specialKey + '__desc')" :key="specialKey + '__' + index">{{ text }}</span>
          <!-- 需要转义的列 -->
          <span v-else :key="specialKey + '__' + index">{{ record[specialKey + '__desc'] }}</span>
        </div>

        <!-- 错误信息 -->
        <div v-show="record[specialKey+'__err_msg']" :key="specialKey+'__err_info'" class="err-msg mt-8px!">
          {{ record[specialKey+'__err_msg'] || ' ' }}
        </div>
      </template>
      <template slot="action" slot-scope="text, record, index">
        <!-- TODO 这个id判断需要优化 -->
        <a-popconfirm
          v-show="(actionColumn && actionColumn.isShow) ? actionColumn.isShow(index) : true"
          title="是否移除?"
          :okText="'是'"
          :cancelText="'否'"
          @confirm="() => onRowDel(index)"
        >
          <a href="javascript:;">移除</a>
        </a-popconfirm>
      </template>
      <!-- 底部新增 -->
      <template v-if="$listeners.Add" slot="footer">
        <div class="sub-footer-invoice">
          <a-button type="dashed" :block="true" icon="plus" style="opacity: 0.8" @click="onRowAdd">新增</a-button>
        </div>
      </template>
    </a-table>
  </div>
</template>

<script>
import { defineComponent } from '@vue/composition-api'
import { isNumber } from 'radash'

export default defineComponent({
  name: 'RuleItem',
  props: {
    rowKey: {
      type: String,
      default: 'id',
      require: true,
    },
    dataSource: {
      type: Array,
      default: () => [],
      require: true,
    },
    size: {
      type: 'default' | 'middle' | 'small',
      default: 'default',
    },
    columns: {
      type: Array,
      default: () => [],
      require: true,
    },
    pagination: {
      type: Boolean,
      default: false,
    },
    showHeader: {
      type: Boolean,
      default: true,
    },
    bordered: {
      type: Boolean,
      default: false,
    },
    skipCheckFields: {
      type: Array,
      default: () => [],
    },
    rules: {
      type: Object,
      default: () => {},
    },
  },
  data() {
    return {
      _dataSource: [],
    }
  },
  watch: {
    // 监听this.dataSource的变化
    dataSource: {
      handler(val) {
        this._dataSource = JSON.parse(JSON.stringify(val))
      },
      immediate: true,
      deep: true,
    }
  },
  computed: {
    // 需要展示的列
    specialKeys() {
      const specialKeys = this.columns.filter(column => !['action'].includes(column.dataIndex)).map(column => column.dataIndex)
      return specialKeys
    },
    actionColumn() {
      return this.columns.find(column => column.dataIndex === 'action')
    },
    // 动态计算子表格的overFlow-x宽度
    dynamicChirldWidth() {
      return this.columns.reduce((acc, col) => acc + col.width, 0)
    },
  },
  methods: {
    // 根据对象获取数据列
    getColumns(obj) {
      const columns = []
      Object.keys(obj).forEach(key => {
        columns.push({
          title: key,
          dataIndex: key,
        })
      })
      return columns
    },
    onRowDel(index) {
      if (isNumber(this.dataSource.length) && this.dataSource.length === 1) {
        this.$message.warn('至少存在一条数据')
        return
      }
      this.$emit('rowDel', index)
    },
    onRowAdd() {
      // 如果刚加的一条数据每一个字段都没有值，不允许新增
      const lastItem = this._dataSource[this._dataSource.length - 1]
      if (!lastItem) {
        this.$emit('rowAdd', 'add')
        return
      }
      const skipCheckFields = this.skipCheckFields
      // 获取需要检查的字段
      const checkFields = Object.keys(lastItem).filter(key => !skipCheckFields.includes(key) && !key.endsWith('__options') && !key.endsWith('__err_msg'))
      // 检查是否可以新增
      // 如果最后一条数据的所有需检查字段都为空，canAdd为false，否则为true
      const canAdd = checkFields.some(key => {
        const value = lastItem[key]
        if (typeof value === 'string') {
          return value.trim().length > 0
        }
        if (Array.isArray(value)) {
          return value.length > 0
        }
        // 既不是数组也不是字符串，直接返回false
        return false
      })
      if (canAdd) {
        this.$emit('rowAdd', 'add')
      }
    },
    /** 选择框改变 */
    onSelectChange(index, column, value) {
      this.$emit('cellChange', index, column, value)
    },
    onCellChange(index, column, value, isVal = false, options) {
      this.$emit('cellChange', index, column, value, isVal, options)
    }
  },
  created() {
  },
  mounted() {
  }
})
</script>

<style lang="less" scoped>
.ruler-item-container {
  ::v-deep .ant-table-tbody > tr:not(:last-child) > td {
    border-bottom: 1px solid #e8e8e8 !important;
  }
  ::v-deep .ant-table-tbody > tr > td {
    border-right: 0;
    vertical-align: middle !important;
  }
  ::v-deep .ant-table-bordered .ant-table-tbody > tr > td {
    border-right: 1px solid #e8e8e8;
  }
}
::v-deep .ant-table-tbody > tr:hover:not(.ant-table-expanded-row):not(.ant-table-row-selected) > td {
  background: transparent;
}

::v-deep .ant-form-item{
  margin-bottom: 0 !important;
}
::v-deep .ant-form-item-children{
  display: block;
  width: 100% !important;
  // 数字输入框,单选组
  .ant-input-number, .ant-radio-group, .ant-select{
    width: 100% !important;
  }
  .ant-radio-group{
    text-align: left;
  }
}
.err{
  ::v-deep .ant-select-selection, ::v-deep .ant-input-number-input-wrap {
    border: 1px solid red;
  }
}
::v-deep .ant-input.err {
  border: 1px solid red;
}
::v-deep .ant-input:focus.err {
  border: 1px solid red;
  box-shadow: 0 0 0 2px rgba(255, 24, 24, 0.2)
}
::v-deep .ant-table-scroll .ant-table-body{
  overflow-x: auto !important;
}

.err-msg{
  font-size: 12px;
  line-height: 1.5;
  text-align: left;
  color: red;
}
</style>
