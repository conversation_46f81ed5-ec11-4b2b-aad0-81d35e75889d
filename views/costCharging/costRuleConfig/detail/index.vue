<script lang="ts">
import { defineComponent } from '@vue/composition-api'
import { costEnums, getCalculateItemEnum } from '_/api'
import { operateCalRuleApi, operateRuleRouteApi, queryCalculatePrecedenceApi, queryCalEleGroupApi, queryCalRuleByRuleIdApi, queryCalRulesByRouteIdApi, queryCheckCodesAndItServiceApi, queryCommonRuleListApi, queryRuleRouteListApi } from '_/api/costCharging'
import { ModalType, TableColumn } from '_/types'
import { ModalTypeEnum } from '_/utils/constant'
import { CalculatePrecedenceBaseInfo, CalEleGroup, CalElementDetail, CalFeeRule, CalFeeRuleDetailNormal, CalFeeRuleDisplay, CheckCodesAndItService, CommonRule, RouteBaseInfo, RouteItem, RouteListDisplay, RuleForm } from '_/types/costCharging'
import { EnumBase } from '_/types/enums/enums'
import { getLabelFromEnum, shortenString, toThousands } from '_/utils'
import { isSuccess } from '_/utils/code'
import { OperateCalRuleReq, OperateCalRuleRes, OperateRuleRouteReq, OperateRuleRouteRes, QueryCalEleGroupRes, QueryCommonRuleListReq } from '_/api/types/costCharging'
import RuleItem from '../RuleItem/index.vue'
import { CellOptions } from '../RuleItem'
import { debounce, isNumber, isObject, uid, clone } from 'radash'
import { LayoutConstant } from '_/assets/constant/layout'
import moment from 'moment'

const units = {
  'FIXED': '元',
  'RATE': '%'
} as Record<string, string>
// 值域映射枚举
const FieldTypeMap = {
  PRIMITIVE: 'primitive',
  OBJECT: 'object'
} as const
// 值域类型
type FieldType = (typeof FieldTypeMap)[keyof typeof FieldTypeMap]

type TradeRange = {
  amountStart: number | string
  amountEnd: number | string
}
/**
 * @description 校验函数的类型
 * 需要知道第几行 第几列 数据 值类型 对象字段
 * */
type Validator = (index: number, column: string, data: any, valueType: FieldType, objectField?: string) => string
/**
 * @description 穿梭框元数据
 * */
type TransferItem = {
  key: string
  title: string
  description?: string
}
/**
 * @description 穿梭框数据操作数据类型
 * */
type TransferCollection = {
  [key: string]: {
    dataSource: TransferItem[],
    targetKeys: string[],
  }
}
/**
 * @description 需要特殊配置的计费元素
 */
const SpecialCalEleMap = {
  MERCHANT_NO: 'MERCHANT_NO' as const, // 报备商户号
  INDUSTRY_CODE: 'INDUSTRY_CODE' as const, // 行业编码
  PAY_INTERFACE_CODE: 'PAY_INTERFACE_CODE' as const, // 实体通道编码
}
// 元素组
const SpecialCalEles = Object.values(SpecialCalEleMap) as string[]

// 定义需要费用类型的清算机构
const ClearingOrgMap = {
  UNION_PAY: 'UNION_PAY',
  AMEX: 'AMEX'
}
const FeeTypeClearingOrg = Object.values(ClearingOrgMap) as string[]

export default defineComponent({
  name: 'CostRuleConfigDetail',
  components: {
    RuleItem,
  },
  data() {
    // 计算规则展示列
    const calRuleColumns = [
      {
        title: '交易类型',
        width: 80,
        dataIndex: 'tradeType',
        key: 'tradeType',
        scopedSlots: { customRender: 'tradeType' },
      },
      {
        title: '计费类型',
        dataIndex: 'calFeeType',
        width: 80,
        key: 'calFeeType',
        scopedSlots: { customRender: 'calFeeType' },
      },
      {
        title: '费用类型',
        dataIndex: 'feeType',
        width: 100,
        key: 'feeType',
        scopedSlots: { customRender: 'feeType' },
      },
      {
        title: '交易区间',
        dataIndex: 'tradeRange',
        width: 140,
        key: 'tradeRange',
        scopedSlots: { customRender: 'tradeRange' },
      },
      {
        title: '按比例退款',
        dataIndex: 'refundByProportion',
        width: 100,
        key: 'refundByProportion',
        scopedSlots: { customRender: 'refundByProportion' },
      },
      {
        title: '计费规则',
        dataIndex: 'calRuleDesc',
        width: 140,
        key: 'calRuleDesc',
        ellipsis: true,
        scopedSlots: { customRender: 'calRuleDesc' },
      },
      {
        title: '保底',
        dataIndex: 'minValue',
        width: 160,
        key: 'minValue',
        scopedSlots: { customRender: 'minValue' },
      },
      {
        title: '封顶',
        dataIndex: 'maxValue',
        width: 160,
        key: 'maxValue',
        scopedSlots: { customRender: 'maxValue' },
      },
      {
        title: '生效期间',
        dataIndex: 'validityTimeRange',
        width: 160,
        key: 'validityTimeRange',
        scopedSlots: { customRender: 'validityTimeRange' },
      },
      {
        title: '操作',
        width: 140,
        dataIndex: 'operation',
        key: 'operation',
        scopedSlots: { customRender: 'operation' },
      },
    ]

    return {
      getLabelFromEnum,
      isNumber,
      moment,
      shortenString,
      SpecialCalEles, // 需要特殊配置的计费元素
      FeeTypeClearingOrg, // 需要手续费的清算机构
      SpecialCalEleMap,
      calEleGroup: [] as CalEleGroup[],
      ModalTypeEnum,
      FieldTypeMap,
      calRuleColumns,
      // 表单布局
      layout: {
        layout: 'horizontal',
        labelCol: { span: 4 },
        wrapperCol: { span: 20 },
      },
      // 路由组号
      groupNo: '',
      // 计费元素列表
      elementList: [] as string[],
      // 过滤表单
      filterForm: {} as Record<string, string | string[]>,
      // 路由公共信息
      routeBaseInfo: {
        feeTypes: [] as EnumBase[],
      } as RouteBaseInfo,
      // 路由列表-展示
      routeListDisplay: [] as RouteListDisplay[],
      // 路由列表-展示-All
      routeListDisplayAll: [] as RouteListDisplay[],
      // 路由列表-展示-分页
      displayPage: 1,
      pageSize: 10,
      // 展开的行
      expandedRowKeys: [] as any[],

      // 新增or编辑
      curModalType: '' as ModalType,

      // 计费元素操作新增/修改
      precedenceVisible: false,
      // 当前操作的路由id
      currentRouteId: undefined as number | undefined,
      // 计费优先级新增/编辑表单
      precedenceForm: {
        ruleOrder: undefined as number | undefined,
      },
      // 当前操作的计费元素项
      calElementItem: [] as string[],
      // 当前操作计费元素详情
      calRuleDetail: [] as CalElementDetail[],
      curOperateEle: {
      } as TransferCollection,
      // 配合穿梭框的表单
      addForm: {
      } as { [key: string]: string },
      // 计费元素提交
      calElementSubmiting: false,

      // TODO: 需要记录的父级信息
      // 当前计算规则id
      currentCalRuleId: undefined as number | undefined,
      // 计费规则弹窗显隐
      ruleVisible: false,
      // 计费规则弹窗提交中
      ruleDrawerSubmiting: false,
      // 计费规则新增/编辑表单
      ruleForm: {
        // OP工单编号
        ticketNo: '',
        // OP工单名称
        ticketName: '',
        // 计费类型
        calculateType: 'NO_STEP',
        // 累计期间
        accumulationTime: ['', ''],
        // 生效期间
        validityTime: ['', '']
      } as RuleForm,

      // 手续费tableEdit校验规则
      tableEditValRules: {} as Record<string, { required?: boolean, validator: boolean | Validator, message?: string, fieldType: 'primitive' | 'object' }>,
      // 单元格表单布局
      elesLayout: LayoutConstant,
      // 手续费展示数据
      tableData: [] as CalFeeRuleDisplay[],

      // -----------------------枚举-----------------------
      ENUMS: {} as { [key: string]: EnumBase[] },
      // 清算机构枚举
      clearingOrgEnum: [] as EnumBase[],
      // 渠道标识枚举
      tagsEnum: [] as EnumBase[],
      // 费用类型枚举
      feeTypeEnum: [] as EnumBase[],
      // 结算方式
      settlementTypeEnum: [] as EnumBase[],
      // 计费类型
      calculateTypeEnum: [] as EnumBase[],
      // 交易类型
      tradeTypeEnum: [] as EnumBase[],
      // 卡类型
      cardTypeEnum: [] as EnumBase[],
      // YN枚举
      yNEnum: [] as EnumBase[],
      // 计算类型
      calConfigRuleEnum: [] as EnumBase[],
      // 计费元素枚举列表
      calculateItemEnum: [] as EnumBase[],
      // 计费元素枚举集合
      calculateItemEnumMap: {} as Record<string, EnumBase[]>,
      // 已配置的计费优先级
      configuredCalPrecedence: [] as CalculatePrecedenceBaseInfo[],
      // 对账接口/技术服务商
      checkCodesAndItService: [] as CheckCodesAndItService[],
      // 通用计费规则
      commonRules: [] as CommonRule[],
    }
  },
  computed: {
    // 动态计算子表格的overFlow-x宽度
    dynamicChirldWidth() {
      return this.calRuleColumns.reduce((acc, col) => acc + col.width, 0)
    },
    // 获取路由列表的动态列
    dynamicRoutesColumns() {
      const _columns = [] as TableColumn[]
      // 第一列是排序优先级
      _columns.push({
        title: '优先级',
        dataIndex: 'ruleOrder',
        width: 120,
        key: 'ruleOrder',
      })
      this.elementList.forEach((item: string) => {
        const c = {
          title: getLabelFromEnum(item, this.calculateItemEnum, ['value', 'key']),
          dataIndex: item,
          key: item,
          ellipsis: true,
          scopedSlots: { customRender: item },
        } as TableColumn
        if (['PAY_INTERFACE_CODE', 'MERCHANT_NO'].includes(item)) {
          c.width = 300
        } else {
          c.width = 150
        }
        _columns.push(c)
      })
      // 增加操作列
      _columns.push({
        title: '操作',
        dataIndex: 'operation',
        width: 150,
        key: 'operation',
        scopedSlots: { customRender: 'operation' },
      })
      return _columns
    },
    dynamicRoutesColumnsWidth() {
      return this.dynamicRoutesColumns.reduce((acc, col) => acc + (col.width as number), 0)
    },
    // 新增/修改计费规则-规则列表
    feeColumns() {
      const _columns = []

      // 如果计费类型是单阶梯或累计阶梯
      if (['SINGLE_STEP', 'CUMULATIVE_STEP'].includes(this.ruleForm.calculateType)) {
        _columns.push({
          title: '操作',
          dataIndex: 'action',
          width: 60,
          isShow: (index: number) => {
            const step = this.feeTypeNum > 1 ? this.feeTypeNum : 1
            return index % step === 0
          },
          align: 'center',
          key: 'action',
          scopedSlots: { customRender: 'action' }
        })
        _columns.push({
          title: '交易区间',
          dataIndex: 'tradeRange',
          width: 180,
          isShow: (index: number) => {
            const step = this.feeTypeNum > 1 ? this.feeTypeNum : 1
            return index % step === 0
          },
          align: 'center',
          key: 'tradeRange',
          scopedSlots: { customRender: 'tradeRange' }
        })
      }
      // 如果渠道标识是多段式费用类型
      if (this.feeTypeNum > 1) {
        _columns.push({
          title: '费用类型',
          dataIndex: 'feeType',
          width: 100,
          align: 'center',
          key: 'feeType',
          scopedSlots: { customRender: 'feeType' }
        })
      }
      // 支付
      _columns.push({
        title: '支付',
        dataIndex: 'PAY',
        key: 'PAY',
        width: 450,
        align: 'center',
        scopedSlots: { customRender: 'PAY' }
      })
      // 退款
      _columns.push({
        title: '退款',
        dataIndex: 'REFUND',
        key: 'REFUND',
        width: 450,
        align: 'center',
        scopedSlots: { customRender: 'REFUND' }
      })
      return _columns
    },
    // Table值为对象类型-元素项配置-map
    tableObjectElementMap() {
      // 计算类型
      const calConfigElement = {
        dataIndex: 'calConfigRule',
        title: '计算类型',
        type: 'select',
        span: 12,
        layout: this.elesLayout[12],
        allowClear: true,
        options: this.calConfigRuleEnum.map((item: EnumBase) => ({ label: item.value, value: item.key }))
      }
      // 配置值
      const valueElement = {
        dataIndex: 'value',
        title: '配置值(元)',
        type: 'number',
        precision: 4,
        min: 0,
        step: 0.0001,
        span: 12,
        layout: this.elesLayout[12]
      }
      // 按比例
      const refundByProportionElement = {
        dataIndex: 'refundByProportion',
        title: '按比例',
        type: 'radio',
        span: 24,
        options: this.yNEnum.map((item: EnumBase) => ({ label: item.value, value: item.key })),
        layout: this.elesLayout[24]
      }
      // 保底金额
      const minValueElement = {
        dataIndex: 'minValue',
        title: '保底金额',
        type: 'number',
        min: 0,
        max: 9999999.9999,
        precision: 4,
        step: 0.0001,
        span: 12,
        layout: this.elesLayout[12]
      }
      // 封顶金额
      const maxValueElement = {
        dataIndex: 'maxValue',
        title: '封顶金额',
        type: 'number',
        min: 0.0001,
        max: 9999999.9999,
        span: 12,
        precision: 4,
        step: 0.0001,
        layout: this.elesLayout[12]
      }
      // 交易区间-开始
      const tradeAmountStartElement = {
        dataIndex: 'amountStart',
        type: 'number',
        placeholder: '区间开始(含)',
        span: 24,
        min: 0,
        max: 1000000000000,
        precision: 2,
        isShow: (index: number) => {
          const step = this.feeTypeNum > 1 ? this.feeTypeNum : 1
          return index % step === 0
        },
        step: 0.01,
        layout: this.elesLayout['wrap-24']
      }
      // 交易区间-结束
      const tradeAmountEndElement = {
        dataIndex: 'amountEnd',
        type: 'number',
        placeholder: '区间结束(不含)',
        span: 24,
        min: 0.01,
        max: 1000000000000,
        precision: 2,
        step: 0.01,
        isShow: (index: number) => {
          const step = this.feeTypeNum > 1 ? this.feeTypeNum : 1
          return index % step === 0
        },
        layout: this.elesLayout['wrap-24']
      }
      // 计算公式
      const calFormulaElement = {
        dataIndex: 'calFormula',
        title: '计算公式',
        type: 'input',
        span: 24,
        layout: this.elesLayout[24],
      }

      return {
        // 计算类型
        calConfigElement,
        calConfigElementRequired: {
          ...calConfigElement,
          required: true
        },
        // 配置值
        valueElement,
        valueElementRequired: {
          ...valueElement,
          required: true
        },
        // 保底金额
        minValueElement,
        minValueElementRequired: {
          ...minValueElement,
          required: true
        },
        // 封顶金额
        maxValueElement,
        maxValueElementRequired: {
          ...maxValueElement,
          required: true
        },
        // 按比例退款
        refundByProportionElement,
        refundByProportionElementRequired: {
          required: true,
          ...refundByProportionElement
        },
        // 交易区间-开始
        tradeAmountStartElement,
        tradeAmountStartElementRequired: {
          ...tradeAmountStartElement,
          required: true
        },
        // 交易区间-结束
        tradeAmountEndElement,
        tradeAmountEndElementRequired: {
          ...tradeAmountEndElement,
          required: true
        },
        // 计算公式
        calFormulaElement,
        calFormulaElementRequired: {
          ...calFormulaElement,
          required: true
        }
      }
    },
    // 费用类型个数
    feeTypeNum() {
      const _feeTypes = this.routeBaseInfo.feeTypes || []
      return _feeTypes.length > 1 ? _feeTypes.length : 1
    }
  },
  watch: {
    'precedenceVisible': {
      handler(val: boolean) {
        if (!val) {
          this.handleResetAddForm()
          this.resetSearch()
        }
      },
    },
  },
  async mounted() {
    // 获取路由参数
    this.groupNo = this.$route.params.groupNo
    this.getCheckCodesAndItService()
    this.getCommonRule()
    this.getCalculateItemMapEnum()
    const submitPrecedenceModal = this.submitPrecedenceModal
    this.submitPrecedenceModal = debounce({ delay: 200 }, function() {
      // 这行不检查
      submitPrecedenceModal.call(this)
    })
    const onSubmitRuleModal = this.onSubmitRuleModal
    this.onSubmitRuleModal = debounce({ delay: 200 }, function() {
      // 这行不检查
      onSubmitRuleModal.call(this)
    })
  },
  async activated() {
    await this.init()
  },
  methods: {
    handlePageChange(page: number, pageSize: number) {
      this.displayPage = page
      this.pageSize = pageSize
    },
    disabledDate(current: any) {
      // 只能选择路由生效时间范围内的日期
      const startTime = moment(this.routeBaseInfo.validityStartTime)
      const endTime = moment(this.routeBaseInfo.validityEndTime)
      return current && (current < startTime.startOf('day') || current > endTime.endOf('day'))
    },
    // 生成指定范围的数组
    range(start: number, end: number) {
      const result = []
      for (let i = start; i < end; i++) {
        result.push(i)
      }
      return result
    },
    // 根据计算方式返回配置值的max和title
    getCalConfigRuleMaxAndTitle(calConfigRule: string = 'FIXED') {
      let max = 100; let title = '配置值'
      if (calConfigRule === 'FIXED') {
        max = 99999.9999
        title = '配置值(元)'
      } else if (calConfigRule === 'RATE') {
        max = 100
        title = '配置值(%)'
      }
      return { max, title }
    },
    validateTimeRange(rule: any, value: any, callback: any) {
      if (!value[0] || !value[1]) {
        callback(new Error('生效期间不能为空'))
      }
      // 生效开始时间不能大于生效结束时间
      if (moment(value[0]).isAfter(moment(value[1]))) {
        callback(new Error('生效开始时间不能大于生效结束时间'))
      }
      // 生效期间不能在xx时间之外
      // this.routeBaseInfo.validityStartTime this.routeBaseInfo.validityEndTime
      const startTime = moment(this.routeBaseInfo.validityStartTime)
      const endTime = moment(this.routeBaseInfo.validityEndTime)
      if (moment(value[0]).isBefore(startTime) || moment(value[1]).isAfter(endTime)) {
        callback(new Error('计费规则生效期间不在计费优先级生效期间内，请检查！'))
      }
      callback()
    },
    // 获取对账接口/技术服务商
    async getCheckCodesAndItService() {
      // 只是为了回显，查全部即可
      const res = await queryCheckCodesAndItServiceApi({ clearingOrgCode: '', status: '' })
      const { code, data } = res
      if (!isSuccess(code)) {
        this.$message.error('获取对账接口/技术服务商失败')
      }
      this.checkCodesAndItService = data
    },
    // 获取通用计费规则
    async getCommonRule() {
      const res = await queryCommonRuleListApi({} as QueryCommonRuleListReq)
      const { code, data } = res
      if (!isSuccess(code)) {
        this.$message.error('获取通用计费规则失败')
      }
      this.commonRules = data
    },
    // 获取计算元素规则组集合
    async getCalEleGroup() {
      const res = await queryCalEleGroupApi({ calculatePrecedences: [] })
      const { code, data } = res
      if (!isSuccess(code)) {
        this.$message.error('获取计费元素规则组失败')
      }
      this.calEleGroup = data.map((calEle: CalEleGroup) => {
        let calElesStr = ''
        calElesStr = calEle.calculatePrecedences.reduce((prev: string, item: string, index: number) => {
          const _enum = this.calculateItemEnum.find(_ => _.key === item) || {} as EnumBase
          return prev + ' > ' + (_enum.value || item)
        }, '')
        return {
          ...calEle,
          calculatePrecedencesDesc: calElesStr.replace('>', '')
        }
      })
    },
    initPagination() {
      this.displayPage = 1
      this.pageSize = 10
    },
    // 初始化
    async init() {
      this.initPagination()
      // 获取枚举数据
      await this.getEnums()
      // 获取计费优先级
      this.getCalculatePrecedence()
      // 获取路由列表
      await this.getRouteList()
      // 获取展开的行
      this.expandedRowKeys = []
    },
    // 获取枚举数据
    async getEnums() {
      const res = await costEnums()
      const { code, data } = res
      if (!isSuccess(code)) {
        this.$message.error('获取枚举数据失败')
        // 清理枚举
        this.clearingOrgEnum = []
        this.feeTypeEnum = []
        this.settlementTypeEnum = []
        this.tradeTypeEnum = []
        this.cardTypeEnum = []
        this.calculateTypeEnum = []
        this.yNEnum = []
        this.calConfigRuleEnum = []
        this.calculateItemEnum = []
        return
      }
      this.clearingOrgEnum = data.clearingOrgEnum
      this.tagsEnum = data.payInterfaceTagEnum
      this.feeTypeEnum = data.feeTypeEnum
      this.settlementTypeEnum = data.settlementTypeEnum
      this.tradeTypeEnum = data.tradeTypeEnum
      this.cardTypeEnum = data.cardTypeEnum
      this.calculateTypeEnum = data.calculateTypeEnum
      this.yNEnum = data.yNEnum
      this.calConfigRuleEnum = data.calConfigRuleEnum
      this.calculateItemEnum = data.calculateItemEnum
      // 全部保存
      this.ENUMS = data
      // 获取计算元素规则组
      await this.getCalEleGroup()
    },
    // 获取计费元素集合枚举
    async getCalculateItemMapEnum() {
      const res = await getCalculateItemEnum()
      const { code, data } = res
      if (!isSuccess(code)) {
        this.$message.error('获取计费元素枚举失败')
      }
      Object.keys(data).forEach((key: string) => {
        this.$set(this.calculateItemEnumMap, key, data[key])
      })
    },
    // 获取已配置的计费优先级
    async getCalculatePrecedence() {
      const res = await queryCalculatePrecedenceApi({ calculatePrecedences: [] })
      const { code, data } = res
      if (!isSuccess(code)) {
        this.$message.error('获取计费优先级失败')
        return
      }
      this.configuredCalPrecedence = data
    },
    // 获取路由列表并初始化数据
    async getRouteList() {
      const res = await queryRuleRouteListApi({ groupNo: this.groupNo })
      const { code, data } = res

      if (!isSuccess(code)) {
        this.$message.error(`获取路由列表失败: ${code}`)
        return
      }
      if (data.length === 0) {
        this.$message.warning('获取路由列表异常')
        return
      }
      // 获取第一条数据
      const firstRoute = data[0]
      // 初始化路由列表基本信息
      this.initBaseInfo(firstRoute)

      // 更新路由展示列
      this.updateRouteListDisplay(data)
    },
    // 初始化基本信息
    initBaseInfo(route: RouteItem) {
      // 获取通用计费规则
      this.routeBaseInfo.clearingOrgCode = route.clearingOrgCode || ''
      // this.routeBaseInfo.feeTypeNum = route.feeTypeNum || 1
      this.routeBaseInfo.feeTypes = (route.feeTypes || []).map((item: string) => {
        return {
          key: item,
          value: getLabelFromEnum(item, this.feeTypeEnum, ['value', 'key']),
        } as EnumBase
      })

      if (['OTHER'].includes(route.clearingOrgCode)) {
        this.routeBaseInfo.itServiceVendor = route.itServiceVendor || ''
      } else {
        this.routeBaseInfo.checkCode = route.checkCode || ''
      }
      this.routeBaseInfo.settlementType = route.settlementType || ''
      // 通用计费规则id
      this.routeBaseInfo.parentId = route.parentId
      this.routeBaseInfo.calculatePrecedenceId = route.calculatePrecedenceId || 0
      this.routeBaseInfo.validityStartTime = route.validityStartTime || ''
      this.routeBaseInfo.validityEndTime = route.validityEndTime || ''
      // 获取计费优先级元素项并更新动态计费元素列
      this.elementList = JSON.parse(JSON.stringify(route.calculatePrecedenceItem)) as string[]
      // 初始化过滤表单
      this.filterForm = {}
      this.elementList.forEach((item: string) => {
        this.$set(this.filterForm, item, SpecialCalEles.includes(item) ? '' : [])
      })
    },
    // 根据计费优先级id获取对应的计费优先级元素项
    getCalculatePrecedenceElement(id: number): string[] {
      const element = this.configuredCalPrecedence.find((item: CalculatePrecedenceBaseInfo) => item.id === id)
      if (element) {
        return element.calculatePrecedence
      }
      return []
    },
    // 更新路由展示列表
    updateRouteListDisplay(routes: RouteItem[]) {
      const _routes = JSON.parse(JSON.stringify(routes)) as RouteItem[]
      // 更新路由展示列表
      this.routeListDisplay = _routes.map((route: RouteItem) => {
        const display: RouteListDisplay = {
          // 路由id
          id: route.id,
          // 排序优先级
          ruleOrder: route.ruleOrder,
          calculatePrecedenceItem: route.calculatePrecedenceItem,
          calRuleDetail: route.calRuleDetail,
          // 动态计费元素列
          // 扩展-规则列表
          expandedRowRender: [],
        }
        route.calRuleDetail.forEach((item: { key: string, value: any }) => {
          this.$set(display, item.key, item.value)
        })
        return display
      })
      this.routeListDisplayAll = clone(this.routeListDisplay)
    },
    // 展开行
    async handleExpand(expanded: boolean, record: RouteListDisplay) {
      // 根据路由id获取计费规则列表
      if (expanded) {
        this.expandedRowKeys.push(record.id)
        const calRules = await this.getCalRulesByRouteId(record.id)
        const calRulesDisplay = calRules.map((item: CalFeeRule) => {
          // 计算计费规则展示值
          return {
            ...item,
            tradeRange: {
              amountStart: item.amountStart,
              amountEnd: item.amountEnd,
            },
            validityTimeRange: {
              validityStartTime: item.validityStartTime,
              validityEndTime: item.validityEndTime,
            },
            calRuleDesc: this.handleVisualizeRule(item)
          }
        })
        this.$set(record, 'expandedRowRender', calRulesDisplay)
        // 需要对应上
      } else {
        this.expandedRowKeys = this.expandedRowKeys.filter((item: number) => item !== record.id)
      }
    },
    // 根据路由id获取计费规则列表
    async getCalRulesByRouteId(routeId: number): Promise<CalFeeRule[]> {
      const res = await queryCalRulesByRouteIdApi({ calRouteId: routeId })
      const { code, data, message } = res
      if (!isSuccess(code)) {
        this.$message.error(message || '获取计费规则列表失败')
        return []
      }
      return (JSON.parse(JSON.stringify(data)) || []) as CalFeeRule[]
    },
    // 新增计费优先级Modal
    async handleAddPrecedenceModal() {
      this.curModalType = ModalTypeEnum.ADD
      // 清理当前操作的路由id
      this.currentRouteId = undefined
      // 更新计费元素枚举映射集合
      await this.getCalculateItemMapEnum()
      // 初始化计费元素
      this.initTransferCollection(this.getCalElementDetail())
    },
    // 编辑计费优先级Modal
    async handleEditPrecedenceModal(record: RouteListDisplay) {
      this.curModalType = ModalTypeEnum.EDIT
      // 记录当前操作的路由id
      this.currentRouteId = record.id
      // 更新计费元素枚举映射集合
      await this.getCalculateItemMapEnum()
      // 更新计费优先级表单字段
      this.precedenceForm.ruleOrder = record.ruleOrder
      // 处理需要操作的计费元素数据
      this.initTransferCollection(record.calRuleDetail.length > 0 ? record.calRuleDetail : this.getCalElementDetail())
    },
    // 校验计费元素是否至少配置一项
    validatePrecedenceForm(): boolean {
      let _valid = false
      Object.keys(this.curOperateEle).forEach((key: string) => {
        if (_valid) return
        if (this.curOperateEle[key].targetKeys.length === 0) {
          _valid = false
        } else {
          _valid = true
        }
      })
      return _valid
    },
    // 提交计费优先级
    submitPrecedenceModal() {
      this.calElementSubmiting = true
      // 校验
      this.$refs.precedenceForm.validate((valid: boolean) => {
        if (valid) {
          const valid = this.validatePrecedenceForm()
          if (valid) {
            // 封装请求数据
            const requestData: OperateRuleRouteReq = {} as OperateRuleRouteReq
            if (this.curModalType === ModalTypeEnum.EDIT) {
              requestData.id = this.currentRouteId as number
            }
            // 基本信息
            requestData.clearingOrgCode = this.routeBaseInfo.clearingOrgCode
            requestData.ruleOrder = this.precedenceForm.ruleOrder as number
            requestData.settlementType = this.routeBaseInfo.settlementType
            if (['OTHER'].includes(this.routeBaseInfo.clearingOrgCode)) {
              requestData.itServiceVendor = this.routeBaseInfo.itServiceVendor
            } else {
              requestData.checkCode = this.routeBaseInfo.checkCode
            }
            requestData.parentId = this.routeBaseInfo.parentId
            requestData.calculatePrecedenceId = this.routeBaseInfo.calculatePrecedenceId
            requestData.validityStartTime = this.routeBaseInfo.validityStartTime
            requestData.validityEndTime = this.routeBaseInfo.validityEndTime
            // 获取已选择计费元素
            requestData.calRuleDetail = Object.keys(this.curOperateEle).map((key: string) => {
              return {
                key,
                value: this.curOperateEle[key].targetKeys,
              }
            })
            // 提交请求
            operateRuleRouteApi(requestData).then((res: OperateRuleRouteRes) => {
              const { code, message } = res
              if (!isSuccess(code)) {
                this.$message.error(message || '新增/修改计费优先级失败')
              } else {
                this.$message.success('操作成功')
                this.onClosePrecedenceModal()
                // 初始化列表数据
                this.init()
              }
            }).finally(() => {
              this.calElementSubmiting = false
            })
          } else {
            this.calElementSubmiting = false
            this.$message.error('计费元素至少配置一项')
          }
        } else {
          this.calElementSubmiting = false
        }
      })
    },
    // 关闭计费元素弹窗
    onClosePrecedenceModal() {
      this.precedenceVisible = false
      // 初始化表单
      this.precedenceForm.ruleOrder = undefined
      // 清理当前操作的计费元素
      this.resetTransferCollection()
    },
    /**
     * 处理穿梭框选择变化
     * @param nextTargetKeys 目标选择项
     * @param direction 方向
     * @param moveKeys 移动的项
     * @param ele 计费元素
     */
    handleChange(nextTargetKeys: string[], direction: 'left'|'right', moveKeys: string[], ele: string) {
      // 根据ele修改
      this.$set(this.curOperateEle, ele, {
        dataSource: this.curOperateEle[ele].dataSource,
        targetKeys: nextTargetKeys,
      })
    },
    // 获取CalElementDetail[] 通过this.elementList
    getCalElementDetail(): CalElementDetail[] {
      const _calRuleDetail: CalElementDetail[] = []
      this.elementList.forEach((item: string) => {
        _calRuleDetail.push({
          key: item,
          value: [],
        })
      })
      return _calRuleDetail
    },
    // 初始化curOperateEle 当前操作的计费元素配置
    initTransferCollection(calRuleDetail: CalElementDetail[]) {
      // 重置curOperateEle
      this.resetTransferCollection()
      const _calRuleDetail = Array.isArray(calRuleDetail) ? calRuleDetail : []
      // 动态初始化
      _calRuleDetail.forEach((item: CalElementDetail) => {
        // 如果是报备商户号，值即是targetKeys，其他可以通过枚举获取
        const _transferData: TransferItem[] = []
        if (SpecialCalEles.includes(item.key)) {
          item.value.forEach((ele: string) => {
            _transferData.push({
              key: ele,
              title: ele,
            })
          })
        } else {
          if (Array.isArray(this.calculateItemEnumMap[item.key])) {
            this.calculateItemEnumMap[item.key].forEach((item: EnumBase) => {
              if (item.key !== 'ALL') {
                _transferData.push({
                  key: item.key,
                  title: item.value,
                })
              }
            })
          } else {
            this.$message.error(`${item.key} 枚举数据异常`)
          }
        }
        this.$set(this.curOperateEle, item.key, {
          dataSource: _transferData,
          targetKeys: JSON.parse(JSON.stringify(item.value)),
        })
      })

      this.$nextTick(() => {
        this.precedenceVisible = true
      })
    },
    // 重置curOperateEle 当前操作的计费元素配置
    resetTransferCollection() {
      this.curOperateEle = {}
    },

    // 新增计费规则弹窗
    handleAddRuleModal(record: RouteListDisplay) {
      this.curModalType = ModalTypeEnum.ADD
      // 更新当前操作的路由id
      this.currentRouteId = record.id
      // 重设表单
      this.resetRuleForm()
      // 初始化表格验证
      this.initRuleTableValRules()
      // 准备表格数据
      this.prepareRuleData([], this.routeBaseInfo.clearingOrgCode, this.ruleForm.calculateType, this.feeTypeNum)
      // 打开弹窗
      this.ruleVisible = true
    },
    // 编辑计费规则弹窗
    async handleEditRuleModal(calRuleId: number, record: RouteListDisplay) {
      this.curModalType = ModalTypeEnum.EDIT
      if (!calRuleId) {
        this.$message.error('数据异常，计算规则id为空!')
        return
      }
      if (!record.id) {
        this.$message.error('数据异常，路由id为空!')
        return
      }
      // 记录当前操作的路由id
      this.currentRouteId = record.id
      // 记录当前操作的计算规则id
      this.currentCalRuleId = calRuleId
      // 根据计算规则id获取计算规则
      const res = await queryCalRuleByRuleIdApi({ ruleId: calRuleId })
      const { code, data, message } = res
      if (!isSuccess(code)) {
        this.$message.error(message || '获取计算规则失败')
      } else {
        // TODO: 编辑计算规则
        // TODO: 处理数据
        // 初始化表单数据
        this.initRuleFormByRule(data)
        // 处理数据为展示数据
        // 根据清算机构+计费类型初始化校验规则
        this.initRuleTableValRules()
        // 处理数据
        const _expectedData = data.calRules
        this.prepareRuleData(_expectedData, this.routeBaseInfo.clearingOrgCode, data.calculateType, this.feeTypeNum)
        // 打开弹窗
        this.ruleVisible = true
      }
    },
    // 关闭新增/编辑计费规则弹窗
    onCloseRuleModal() {
      this.ruleVisible = false
      // TODO: 清理表单数据
      this.resetRuleForm()
      // 清理校验
      this.$refs.ruleForm.clearValidate()
      // TODO: 清理table数据
      this.resetFeeTableData()
    },
    // 提交新增/编辑计费规则
    onSubmitRuleModal() {
      this.ruleDrawerSubmiting = true
      this.$refs.ruleForm.validate((valid: boolean) => {
        if (valid) {
          // 校验
          const hasErr = this.onValidateAll()
          if (hasErr) {
            this.ruleDrawerSubmiting = false
            this.$message.error('检查手续费配置！')
            return
          }
          // 只有单笔阶梯和累计阶梯才会检查交易区间
          if (['SINGLE_STEP', 'CUMULATIVE_STEP'].includes(this.ruleForm.calculateType)) {
            // 检查交易区间是否重叠
            const step = this.feeTypeNum > 1 ? this.feeTypeNum : 1
            const waitCheckData: any[] = []
            this.tableData.forEach((display: CalFeeRuleDisplay, index: number) => {
              if (index % step === 0) {
                waitCheckData.push(display.tradeRange)
              }
            })
            const hasOverlap = this.hasOverlap(waitCheckData)
            if (hasOverlap.hasOverlap) {
              this.ruleDrawerSubmiting = false
              this.$message.error(`交易区间存在重叠！${hasOverlap.overlappingRanges![0].amountStart} - ${hasOverlap.overlappingRanges![0].amountEnd} 和 ${hasOverlap.overlappingRanges![1].amountStart} - ${hasOverlap.overlappingRanges![1].amountEnd}`)
              return
            }
          }
          // 准备待提交数据
          const requestData: OperateCalRuleReq = {} as OperateCalRuleReq
          // 只有编辑有id
          if (this.curModalType === ModalTypeEnum.EDIT) {
            requestData.id = this.currentCalRuleId
          }
          // 基本信息
          requestData.routeId = this.currentRouteId as number
          requestData.ticketNo = this.ruleForm.ticketNo
          requestData.ticketName = this.ruleForm.ticketName
          requestData.calculateType = this.ruleForm.calculateType
          requestData.validityStartTime = this.ruleForm.validityTime[0] as string
          requestData.validityEndTime = this.ruleForm.validityTime[1] as string
          // 详细规则
          const _calculItems: CalFeeRule[] = []
          this.tableData.forEach((row: CalFeeRuleDisplay, index: number) => {
            const _pay = row.PAY
            const _refund = row.REFUND
            const _payItem: CalFeeRule = {
              id: _pay.id,
              calFeeType: _pay.calFeeType,
              calRuleId: _pay.calRuleId,
              refundByProportion: _pay.refundByProportion,
              calConfigRule: _pay.calConfigRule,
              value: _pay.value,
              maxValue: _pay.maxValue,
              minValue: _pay.minValue,
              tradeType: _pay.tradeType,
              feeType: _pay.feeType,
              cardType: _pay.cardType,

            }
            const _refundItem: CalFeeRule = {
              id: _refund.id,
              calFeeType: _refund.calFeeType,
              calRuleId: _refund.calRuleId,
              refundByProportion: _refund.refundByProportion,
              calConfigRule: _refund.calConfigRule,
              value: _refund.value,
              maxValue: _refund.maxValue,
              minValue: _refund.minValue,
              tradeType: _refund.tradeType,
              feeType: _refund.feeType,
              cardType: _refund.cardType,
            }
            // 单笔阶梯需要这两个字段
            if (['SINGLE_STEP', 'CUMULATIVE_STEP'].includes(this.ruleForm.calculateType)) {
              // index / this.feeTypeNum 获取到当前的阶梯 是第几个 如果不是第一个，需要取第一个
              const step = this.feeTypeNum > 1 ? this.feeTypeNum : 1
              // 获取到当前的阶梯 是第几个
              const stepIndex = Math.floor(index % step)
              // 获取到第一个阶梯的index
              const firstRowIndex = index - stepIndex
              let _tradeRange = {} as any
              if (index !== firstRowIndex) {
                const firstRow = this.tableData[firstRowIndex]

                _tradeRange = firstRow.tradeRange
              } else {
                _tradeRange = row.tradeRange as any
              }
              _payItem.amountStart = _tradeRange.amountStart
              _payItem.amountEnd = _tradeRange.amountEnd
              _refundItem.amountStart = _tradeRange.amountStart
              _refundItem.amountEnd = _tradeRange.amountEnd
            }
            // 如果是组合计费需要这两个字段
            if (this.ruleForm.calculateType === 'COMBINED_BILLING') {
              _payItem.calFormula = _pay.calFormula
              _refundItem.calFormula = _refund.calFormula
            }
            _calculItems.push(_payItem)
            _calculItems.push(_refundItem)
          })
          // requestData.accumulationStartTime = this.ruleForm.accumulationTime[0]
          // requestData.accumulationEndTime = this.ruleForm.accumulationTime[1]
          // 获取表格数据
          requestData.calRules = _calculItems

          operateCalRuleApi(requestData).then((res: OperateCalRuleRes) => {
            const { code, message } = res
            if (!isSuccess(code)) {
              this.$message.error(message || '计费规则变更失败')
            } else {
              this.$message.success('操作成功')
              this.onCloseRuleModal()
              // 初始化列表数据
              this.init()
            }
          }).finally(() => {
            this.ruleDrawerSubmiting = false
          })
        } else {
          this.ruleDrawerSubmiting = false
        }
      })
    },

    // 初始化计费规则表单
    initRuleFormByRule(ruleDetail: CalFeeRuleDetailNormal) {
      this.ruleForm = {
        ticketNo: ruleDetail.ticketNo,
        ticketName: ruleDetail.ticketName,
        calculateType: ruleDetail.calculateType,
        accumulationTime: [ruleDetail.accumulationStartTime || '', ruleDetail.accumulationEndTime || ''],
        validityTime: [ruleDetail.validityStartTime || '', ruleDetail.validityEndTime || ''],
      }
    },
    // 获取id
    getNewId(oldId: string = ''): string {
      return uid(7, oldId)
    },
    // 准备计费规则需要的数据
    prepareRuleData(ruleDetail: CalFeeRule[], clearingOrgCode: string, calculateType: string, feeTypeNum: number) {
      // 初始化费用规则table的数据
      const _tableData = [] as CalFeeRuleDisplay[]
      type GroupMap = {
        [key: string]: CalFeeRule[]
      }
      // 数据分组

      // 需要分组的场景
      const groupScenes = ['SINGLE_STEP', 'CUMULATIVE_STEP']
      // 确定分组规则 目前单笔阶梯/累计阶梯 需要根据交易区间进行分组； 通用里是根据卡类型进行分组
      // 如果入参的calculateType在分组场景里，则按照规则分组。如果不在则都存在DEFAULT里
      const groupFun = (calFeeRule: CalFeeRule[]): GroupMap => {
        // 如果不需要分组，则直接返回所有数据
        if (!groupScenes.includes(calculateType)) {
          return {
            DEFAULT: [...ruleDetail]
          } as GroupMap
        }
        // 需要分组 定义分组容器
        const _groupMap: GroupMap = {}
        // 开始分组
        calFeeRule.forEach((rule: CalFeeRule) => {
          // 支取符合逻辑的数据，目前符合的条件是金额
          if (isNumber(rule.amountStart)) {
            if (_groupMap.hasOwnProperty(rule.amountStart)) {
              _groupMap[rule.amountStart].push(rule)
            } else {
              this.$set(_groupMap, rule.amountStart, [rule])
            }
          } else {
            this.$message.error(`${rule.amountStart} 分组key不合理，丢弃`)
          }
        })
        return _groupMap
      }
      const groupMap: GroupMap = groupFun(ruleDetail)
      // 如果分组为空，则直接走添加交易区间逻辑
      if (Object.keys(groupMap).length === 0) {
        this.handleAddTradeRange()
        return
      }
      // 根据清算机构 区分取数流程 银联/联调 根据费用类型区分 每种费用类型取一条支付、一条退款
      // 根据计算类型 区分默认数据生成类型 主要分组有 无阶梯、单笔阶梯/累计阶梯、组合计费

      // 如果是需要区分费用类型的场景
      if (feeTypeNum > 1) {
        // 迭代费用类型,过滤到ALL
        const feeTypeEnums = this.routeBaseInfo.feeTypes.filter((feeTypeItem: EnumBase) => feeTypeItem.key !== 'ALL')
        // 区分分组类型
        if (!groupScenes.includes(calculateType)) {
          // 不是分组类型
          // 迭代费用类型，都从DEFAULT中取
          feeTypeEnums.forEach((feeTypeItem: EnumBase) => {
            // 取一条支付
            const findPayRule = groupMap.DEFAULT.find((rule: CalFeeRule) => rule.tradeType === 'PAY' && rule.feeType === feeTypeItem.key)
            // 取一条退款
            const findRefundRule = groupMap.DEFAULT.find((rule: CalFeeRule) => rule.tradeType === 'REFUND' && rule.feeType === feeTypeItem.key)
            // 确定支付数据
            const payRule = findPayRule || this.getDefaultCalFeeRuleByCalFeeType('PAY', calculateType, feeTypeItem.key)
            // 确定退款数据
            const refundRule = findRefundRule || this.getDefaultCalFeeRuleByCalFeeType('REFUND', calculateType, feeTypeItem.key)
            // 定义展示数据
            const _calFeeRuleDisplay: CalFeeRuleDisplay = {} as CalFeeRuleDisplay
            this.$set(_calFeeRuleDisplay, 'id', this.getNewId())
            // 先放数据
            this.$set(_calFeeRuleDisplay, 'PAY', payRule)
            this.$set(_calFeeRuleDisplay, 'REFUND', refundRule)
            // 先处理特殊场景的展示列字段定义
            // 定义费用类型
            this.$set(_calFeeRuleDisplay, 'feeType', payRule.feeType)
            // 定义费用类型描述 是描述类型的字段
            this.$set(_calFeeRuleDisplay, 'feeType__desc', getLabelFromEnum(payRule.feeType, this.feeTypeEnum, ['value', 'key']))

            // 定义支付退款的元素对象
            if (['NO_STEP', 'SINGLE_STEP', 'CUMULATIVE_STEP'].includes(calculateType)) {
              // 定义支付元素对象
              this.$set(_calFeeRuleDisplay, 'PAY__object', {
                elements: [
                  this.tableObjectElementMap.calConfigElementRequired,
                  {
                    ...this.tableObjectElementMap.valueElementRequired,
                    ...this.getCalConfigRuleMaxAndTitle(payRule.calConfigRule),
                  },
                  this.tableObjectElementMap.minValueElement,
                  this.tableObjectElementMap.maxValueElement,
                ]
              })
              // 定义退款元素对象
              this.$set(_calFeeRuleDisplay, 'REFUND__object', {
                elements: [
                  this.tableObjectElementMap.refundByProportionElementRequired,
                  ...(refundRule.refundByProportion === 'N' ? [
                    this.tableObjectElementMap.calConfigElementRequired,
                    {
                      ...this.tableObjectElementMap.valueElementRequired,
                      ...this.getCalConfigRuleMaxAndTitle(refundRule.calConfigRule),
                    },
                  ] : []),
                  this.tableObjectElementMap.minValueElement,
                  this.tableObjectElementMap.maxValueElement,
                ]
              })
            } else if (calculateType === 'COMBINED_BILLING') {
              // 定义支付元素对象
              this.$set(_calFeeRuleDisplay, 'PAY__object', {
                elements: [
                  this.tableObjectElementMap.calFormulaElementRequired,
                ]
              })
              // 定义退款元素对象
              this.$set(_calFeeRuleDisplay, 'REFUND__object', {
                elements: [
                  this.tableObjectElementMap.refundByProportionElementRequired,
                  ...(refundRule.refundByProportion === 'N' ? [
                    this.tableObjectElementMap.calConfigElementRequired,
                    {
                      ...this.tableObjectElementMap.valueElementRequired,
                      ...this.getCalConfigRuleMaxAndTitle(refundRule.calConfigRule),
                    },
                    this.tableObjectElementMap.calFormulaElementRequired,
                  ] : []),
                ]
              })
            }

            // 插入_tableData
            _tableData.push(_calFeeRuleDisplay)
          })
        } else {
          // 分组类型
          // 按照分组迭代，每个分组中按照费用类型迭代获取，取不到则设置默认值
          // 如果是分组类型，则需要按照分组迭代，然后按照费用类型迭代获取，取不到则设置默认值
          Object.keys(groupMap).forEach((groupKey: string) => {
            feeTypeEnums.forEach((feeTypeItem: EnumBase) => {
              // 取一条支付
              const findPayRule = groupMap[groupKey].find((rule: CalFeeRule) => rule.tradeType === 'PAY' && rule.feeType === feeTypeItem.key)
              // 取一条退款
              const findRefundRule = groupMap[groupKey].find((rule: CalFeeRule) => rule.tradeType === 'REFUND' && rule.feeType === feeTypeItem.key)
              // 确定支付数据
              const payRule = findPayRule || this.getDefaultCalFeeRuleByCalFeeType('PAY', calculateType, feeTypeItem.key)
              // 确定退款数据
              const refundRule = findRefundRule || this.getDefaultCalFeeRuleByCalFeeType('REFUND', calculateType, feeTypeItem.key)

              // 定义展示数据
              const _calFeeRuleDisplay: CalFeeRuleDisplay = {} as CalFeeRuleDisplay
              // 先放数据
              this.$set(_calFeeRuleDisplay, 'id', this.getNewId())
              this.$set(_calFeeRuleDisplay, 'PAY', payRule)
              this.$set(_calFeeRuleDisplay, 'REFUND', refundRule)

              // 先处理特殊场景的展示列字段定义
              // 定义分区列
              this.$set(_calFeeRuleDisplay, 'tradeRange', {
                amountStart: Number(groupKey), // 按照amountStart分组的
                amountEnd: payRule.amountEnd || '' // 每条规则都应该有分组信息，并且每个分组里的这些信息都一样。如果都没有置空
              })
              // 定义 分组__object 是对象类型的字段
              this.$set(_calFeeRuleDisplay, `tradeRange__object`, {
                elements: [
                  this.tableObjectElementMap.tradeAmountStartElementRequired,
                  this.tableObjectElementMap.tradeAmountEndElementRequired,
                ]
              })
              // 定义费用类型
              this.$set(_calFeeRuleDisplay, 'feeType', payRule.feeType)
              // 定义费用类型描述 是描述类型的字段
              this.$set(_calFeeRuleDisplay, 'feeType__desc', getLabelFromEnum(payRule.feeType, this.feeTypeEnum, ['value', 'key']))

              // 定义每种计费类型的元素对象
              // 无阶梯、单笔阶梯、累计阶梯的支付/退款元素对象一致所以一起处理
              if (['NO_STEP', 'SINGLE_STEP', 'CUMULATIVE_STEP'].includes(calculateType)) {
              // 定义支付元素对象
                this.$set(_calFeeRuleDisplay, 'PAY__object', {
                  elements: [
                    this.tableObjectElementMap.calConfigElementRequired,
                    {
                      ...this.tableObjectElementMap.valueElementRequired,
                      ...this.getCalConfigRuleMaxAndTitle(payRule.calConfigRule),
                    },
                    this.tableObjectElementMap.minValueElement,
                    this.tableObjectElementMap.maxValueElement,
                  ]
                })
                // 定义退款元素对象
                this.$set(_calFeeRuleDisplay, 'REFUND__object', {
                  elements: [
                    this.tableObjectElementMap.refundByProportionElementRequired,
                    ...(refundRule.refundByProportion === 'N' ? [
                      this.tableObjectElementMap.calConfigElementRequired,
                      {
                        ...this.tableObjectElementMap.valueElementRequired,
                        ...this.getCalConfigRuleMaxAndTitle(refundRule.calConfigRule),
                      },
                    ] : []),
                    this.tableObjectElementMap.minValueElement,
                    this.tableObjectElementMap.maxValueElement,
                  ]
                })
              } else if (calculateType === 'COMBINED_BILLING') {
              // 定义支付元素对象
                this.$set(_calFeeRuleDisplay, 'PAY__object', {
                  elements: [
                    this.tableObjectElementMap.calFormulaElementRequired,
                  ]
                })
                // 定义退款元素对象
                this.$set(_calFeeRuleDisplay, 'REFUND__object', {
                  elements: [
                    this.tableObjectElementMap.refundByProportionElementRequired,
                    ...(refundRule.refundByProportion === 'N' ? [
                      this.tableObjectElementMap.calConfigElementRequired,
                      {
                        ...this.tableObjectElementMap.valueElementRequired,
                        ...this.getCalConfigRuleMaxAndTitle(refundRule.calConfigRule),
                      },
                      this.tableObjectElementMap.calFormulaElementRequired,
                    ] : []),
                  ]
                })
              }
              // 插入_tableData
              _tableData.push(_calFeeRuleDisplay)
            })
          })
        }
      } else {
        // 其他场景
        // 区分分组类型
        if (!groupScenes.includes(calculateType)) {
          // 不是分组类型
          // 取一条支付数据
          const findPayRule = groupMap.DEFAULT.find((rule: CalFeeRule) => rule.tradeType === 'PAY')
          // 取一条退款数据
          const findRefundRule = groupMap.DEFAULT.find((rule: CalFeeRule) => rule.tradeType === 'REFUND')
          // 确定支付数据
          const payRule = findPayRule || this.getDefaultCalFeeRuleByCalFeeType('PAY', calculateType, '')
          // 确定退款数据
          const refundRule = findRefundRule || this.getDefaultCalFeeRuleByCalFeeType('REFUND', calculateType, '')

          // 定义展示数据
          const _calFeeRuleDisplay: CalFeeRuleDisplay = {} as CalFeeRuleDisplay
          this.$set(_calFeeRuleDisplay, 'id', this.getNewId())
          // 先放数据
          this.$set(_calFeeRuleDisplay, 'PAY', payRule)
          this.$set(_calFeeRuleDisplay, 'REFUND', refundRule)
          // 定义支付退款的元素对象
          if (['NO_STEP', 'SINGLE_STEP', 'CUMULATIVE_STEP'].includes(calculateType)) {
            // 定义支付元素对象
            this.$set(_calFeeRuleDisplay, 'PAY__object', {
              elements: [
                this.tableObjectElementMap.calConfigElementRequired,
                {
                  ...this.tableObjectElementMap.valueElementRequired,
                  ...this.getCalConfigRuleMaxAndTitle(payRule.calConfigRule),
                },
                this.tableObjectElementMap.minValueElement,
                this.tableObjectElementMap.maxValueElement,
              ]
            })
            // 定义退款元素对象
            this.$set(_calFeeRuleDisplay, 'REFUND__object', {
              elements: [
                this.tableObjectElementMap.refundByProportionElementRequired,
                ...(refundRule.refundByProportion === 'N' ? [
                  this.tableObjectElementMap.calConfigElementRequired,
                  {
                    ...this.tableObjectElementMap.valueElementRequired,
                    ...this.getCalConfigRuleMaxAndTitle(refundRule.calConfigRule),
                  },
                ] : []),
                this.tableObjectElementMap.minValueElement,
                this.tableObjectElementMap.maxValueElement,
              ]
            })
          } else if (calculateType === 'COMBINED_BILLING') {
            // 定义支付元素对象
            this.$set(_calFeeRuleDisplay, 'PAY__object', {
              elements: [
                this.tableObjectElementMap.calFormulaElementRequired,
              ]
            })
            // 定义退款元素对象
            this.$set(_calFeeRuleDisplay, 'REFUND__object', {
              elements: [
                this.tableObjectElementMap.refundByProportionElementRequired,
                ...(refundRule.refundByProportion === 'N' ? [
                  this.tableObjectElementMap.calConfigElementRequired,
                  {
                    ...this.tableObjectElementMap.valueElementRequired,
                    ...this.getCalConfigRuleMaxAndTitle(refundRule.calConfigRule),
                  },
                  this.tableObjectElementMap.calFormulaElementRequired,
                ] : []),
              ]
            })
          }
          // 插入_tableData
          _tableData.push(_calFeeRuleDisplay)
        } else {
          // 分组类型
          // 迭代分组
          Object.keys(groupMap).forEach((groupKey: string) => {
            // 取一条支付
            const findPayRule = groupMap[groupKey].find((rule: CalFeeRule) => rule.tradeType === 'PAY')
            // 取一条退款
            const findRefundRule = groupMap[groupKey].find((rule: CalFeeRule) => rule.tradeType === 'REFUND')
            // 确定支付数据
            const payRule = findPayRule || this.getDefaultCalFeeRuleByCalFeeType('PAY', calculateType, '')
            // 确定退款数据
            const refundRule = findRefundRule || this.getDefaultCalFeeRuleByCalFeeType('REFUND', calculateType, '')
            // 定义展示数据
            const _calFeeRuleDisplay: CalFeeRuleDisplay = {} as CalFeeRuleDisplay
            // 先放数据
            this.$set(_calFeeRuleDisplay, 'id', this.getNewId())
            this.$set(_calFeeRuleDisplay, 'PAY', payRule)
            this.$set(_calFeeRuleDisplay, 'REFUND', refundRule)
            // 特殊列
            // 分组列
            this.$set(_calFeeRuleDisplay, 'tradeRange', {
              amountStart: Number(groupKey), // 按照amountStart分组的
              amountEnd: payRule.amountEnd || '' // 每条规则都应该有分组信息，并且每个分组里的这些信息都一样。如果都没有置空
            })
            // 分组__object 是对象类型的字段
            this.$set(_calFeeRuleDisplay, `tradeRange__object`, {
              elements: [
                this.tableObjectElementMap.tradeAmountStartElementRequired,
                this.tableObjectElementMap.tradeAmountEndElementRequired,
              ]
            })
            // 定义支付退款的元素对象
            if (['NO_STEP', 'SINGLE_STEP', 'CUMULATIVE_STEP'].includes(calculateType)) {
              // 定义支付元素对象
              this.$set(_calFeeRuleDisplay, 'PAY__object', {
                elements: [
                  this.tableObjectElementMap.calConfigElementRequired,
                  {
                    ...this.tableObjectElementMap.valueElementRequired,
                    ...this.getCalConfigRuleMaxAndTitle(payRule.calConfigRule),
                  },
                  this.tableObjectElementMap.minValueElement,
                  this.tableObjectElementMap.maxValueElement,
                ]
              })
              // 定义退款元素对象
              this.$set(_calFeeRuleDisplay, 'REFUND__object', {
                elements: [
                  this.tableObjectElementMap.refundByProportionElementRequired,
                  ...(refundRule.refundByProportion === 'N' ? [
                    this.tableObjectElementMap.calConfigElementRequired,
                    {
                      ...this.tableObjectElementMap.valueElementRequired,
                      ...this.getCalConfigRuleMaxAndTitle(refundRule.calConfigRule),
                    },
                  ] : []),
                  this.tableObjectElementMap.minValueElement,
                  this.tableObjectElementMap.maxValueElement,
                ]
              })
            } else if (calculateType === 'COMBINED_BILLING') {
              // 定义支付元素对象
              this.$set(_calFeeRuleDisplay, 'PAY__object', {
                elements: [
                  this.tableObjectElementMap.calFormulaElement,
                ]
              })
              // 定义退款元素对象
              this.$set(_calFeeRuleDisplay, 'REFUND__object', {
                elements: [
                  this.tableObjectElementMap.refundByProportionElementRequired,
                  ...(refundRule.refundByProportion === 'N' ? [
                    this.tableObjectElementMap.calConfigElementRequired,
                    {
                      ...this.tableObjectElementMap.valueElementRequired,
                      ...this.getCalConfigRuleMaxAndTitle(refundRule.calConfigRule),
                    },
                    this.tableObjectElementMap.calFormulaElementRequired,
                  ] : []),
                ]
              })
            }
            // 插入_tableData
            _tableData.push(_calFeeRuleDisplay)
          })
        }
      }
      this.tableData = _tableData
    },
    /**
     * 根据计费类型和交易类型获取默认计费规则
     * 除了以下三个值外不进行赋值，都置为''
     * @param tradeType 交易类型
     * @param calFeeType 计费类型
     * @param feeType 费用类型
     */
    getDefaultCalFeeRuleByCalFeeType(tradeType: string, calFeeType: string, feeType: string): CalFeeRule {
      const calFeeRule = {} as CalFeeRule
      const _calFeeSence = ['NO_STEP', 'SINGLE_STEP', 'CUMULATIVE_STEP']
      if (tradeType === 'PAY') {
        if (_calFeeSence.includes(calFeeType)) {
          calFeeRule.calConfigRule = 'FIXED'
          // 保底
          calFeeRule.minValue = ''
          // 封顶
          calFeeRule.maxValue = ''
        } else if (calFeeType === 'COMBINED_BILLING') {
          calFeeRule.calFormula = ''
        }
      } else if (tradeType === 'REFUND') {
        // 共有字段
        calFeeRule.refundByProportion = ''
        calFeeRule.calConfigRule = ''
        calFeeRule.value = ''
        // 特殊存在字段
        if (_calFeeSence.includes(calFeeType)) {
          // 保底
          calFeeRule.minValue = ''
          // 封顶
          calFeeRule.maxValue = ''
        } else if (calFeeType === 'COMBINED_BILLING') {
          calFeeRule.calFormula = ''
        }
      }
      // 定义交易类型
      calFeeRule.tradeType = tradeType
      // 定义计费类型
      calFeeRule.calFeeType = calFeeType
      // 定义费用类型
      calFeeRule.feeType = feeType
      return calFeeRule
    },
    // 初始化费用规则table规则
    // 根据column的类型，进行不同的校验。计费类型取值 this.ruleForm.calculateType
    initRuleTableValRules() {
      this.tableEditValRules = {}
      // 公共校验
      this.$set(this.tableEditValRules, 'calConfigRule', {
        validator: (index: number, column: string, data: any) => {
          const _data = this.tableData[index][column]
          // 组合类型，支付不校验
          if (this.ruleForm.calculateType === 'COMBINED_BILLING' && column === 'PAY') {
            return ''
          }
          // 退款，当_data.refundByProportion !== 'N'时，不校验
          if (column === 'REFUND' && _data.refundByProportion !== 'N') {
            return ''
          }
          // 退款 _data.refundByProportion === 'N' 时，存在calFormula时，不必填
          if (column === 'REFUND' && _data.refundByProportion === 'N' && _data.calFormula) {
            return ''
          }
          // 必填
          if (Array.isArray(data) && data.length === 0) {
            return '必填'
          }
          if (data === '' || data === null || data === undefined) {
            return '必填'
          }
          return ''
        },
        fieldType: 'object',
        parentKeys: ['PAY', 'REFUND'],
        message: '请输入',
      })
      this.$set(this.tableEditValRules, 'value', {
        validator: (index: number, column: string, data: any) => {
          const _data = this.tableData[index][column]
          // 组合类型，支付不校验
          if (this.ruleForm.calculateType === 'COMBINED_BILLING' && column === 'PAY') {
            return ''
          }
          // 退款，当_data.refundByProportion !== 'N'时，不校验
          if (column === 'REFUND' && _data.refundByProportion !== 'N') {
            return ''
          }
          if (column === 'REFUND' && _data.refundByProportion === 'N' && _data.calFormula) {
            return ''
          }
          // 必填
          if (Array.isArray(data) && data.length === 0) {
            return '必填'
          }
          if (!isNumber(data)) {
            return '必填'
          }
          return ''
        },
        parentKeys: ['PAY', 'REFUND'],
        fieldType: 'object',
        message: '请输入',
      })
      this.$set(this.tableEditValRules, 'refundByProportion', {
        validator: (index: number, column: string, data: any) => {
          // 支付的不校验
          if (column === 'PAY') {
            return ''
          }
          if (Array.isArray(data) && data.length === 0) {
            return '必填'
          }
          if (data === '' || data === null || data === undefined) {
            return '必填'
          }
          return ''
        },
        fieldType: 'object',
        parentKeys: ['PAY', 'REFUND'],
        message: '请选择',
      })

      // 特定的计费类型才会有的
      if (this.ruleForm.calculateType === 'COMBINED_BILLING') {
        // 组合计费
        // 计费公式
        this.$set(this.tableEditValRules, 'calFormula', {
          validator: (index: number, column: string, data: any) => {
            // 满足条件
            // TODO： 需要通过校验还是长度直接控制是一个问题
            if (data && (data as string).length > 200) {
              return '最大可输入200字'
            }
            // 支付时候一定必填
            if (column === 'PAY') {
              // 必填
              if (Array.isArray(data) && data.length === 0) {
                return '必填'
              }
              if (data === '' || data === null || data === undefined) {
                return '必填'
              }
            } else if (column === 'REFUND') {
              // 退款的时候，当refundByProportion === 'N'时，必填 计算方式和配置值都没有值时必填
              if (data.refundByProportion === 'N' && (!data.calFormula && !data.value)) {
                return '必填'
              }
            }
            return ''
          },
          fieldType: 'object',
          parentKeys: ['PAY', 'REFUND'],
          message: '请输入',
        })
        // 支付整体校验，只有一个元素，不需要了
        // this.$set(this.tableEditValRules, 'PAY', {
        //   validator: (index: number, column: string, data: any) => {
        //     if (!data.calFormula) {
        //       return '计算公式必填'
        //     }
        //     return ''
        //   },
        //   fieldType: 'primitive',
        // })
        // 退款整体校验
        this.$set(this.tableEditValRules, 'REFUND', {
          validator: (index: number, column: string, data: any) => {
            if (data.refundByProportion === 'N') {
              if (!data.calFormula && !isNumber(data.value) && !data.calConfigRule) {
                return '非按比例退款时，计算方式或计算公式至少配置一项'
              } else if (data.calFormula && (isNumber(data.value) || data.calConfigRule)) {
                return '非按比例退款时，计算方式或计算公式只能选择一种'
              }
            }
            return ''
          },
          message: '请选择',
          fieldType: 'primitive',
        })
      } else if (['NO_STEP', 'SINGLE_STEP', 'CUMULATIVE_STEP'].includes(this.ruleForm.calculateType)) {
        // 这三种有的
        this.$set(this.tableEditValRules, 'minValue', {
          validator: false,
          fieldType: 'object',
          parentKeys: ['PAY', 'REFUND'],
          message: '请输入',
        })
        this.$set(this.tableEditValRules, 'maxValue', {
          validator: false,
          fieldType: 'object',
          parentKeys: ['PAY', 'REFUND'],
          message: '请输入',
        })
        this.$set(this.tableEditValRules, 'PAY', {
          validator: (index: number, column: string, data: any) => {
            if (+data.maxValue === 0) {
              return ''
            }
            if (+data.minValue > +data.maxValue) {
              return '保底金额不能大于封顶金额'
            }
            return ''
          },
          fieldType: 'primitive',
        })
        this.$set(this.tableEditValRules, 'REFUND', {
          validator: (index: number, column: string, data: any) => {
            if (+data.maxValue === 0) {
              return ''
            }
            if (+data.minValue > +data.maxValue) {
              return '保底金额不能大于封顶金额'
            }
            return ''
          },
          message: '请选择',
          fieldType: 'primitive',
        })
        // 有交易区间的，金额区间相关
        if (['SINGLE_STEP', 'CUMULATIVE_STEP'].includes(this.ruleForm.calculateType)) {
          this.$set(this.tableEditValRules, 'amountStart', {
            validator: (index: number, column: string, data: any) => {
              const step = this.feeTypeNum > 1 ? this.feeTypeNum : 1
              if (this.feeTypeNum > 1 && index % step !== 0) {
                return ''
              }
              if (!isNumber(data)) {
                return '必填'
              }
              return ''
            },
            fieldType: 'object',
            parentKeys: ['tradeRange'],
            message: '请输入',
          })
          this.$set(this.tableEditValRules, 'amountEnd', {
            validator: (index: number, column: string, data: any) => {
              const step = this.feeTypeNum > 1 ? this.feeTypeNum : 1
              if (this.feeTypeNum > 1 && index % step !== 0) {
                return ''
              }
              if (!isNumber(data)) {
                return '必填'
              }
              return ''
            },
            fieldType: 'object',
            parentKeys: ['tradeRange'],
            message: '请输入',
          })
          this.$set(this.tableEditValRules, 'tradeRange', {
            validator: (index: number, column: string, data: any) => {
              // 如果当前行是银联网联，则不校验
              const step = this.feeTypeNum > 1 ? this.feeTypeNum : 1
              if (this.feeTypeNum > 1 && index % step !== 0) {
                return ''
              }
              if (!isNumber(data.amountStart) || !isNumber(data.amountEnd)) {
                return '交易区间必填'
              }
              if (isNumber(data.amountStart) && isNumber(data.amountEnd)) {
                if (data.amountStart > data.amountEnd) {
                  return '区间开始不能大于区间结束'
                }
              }
              if (data.amountStart === data.amountEnd) {
                return '区间开始不能等于区间结束'
              }
              return ''
            },
            fieldType: 'primitive',
          })
        }
      }
    },
    // 重置计费规则表单
    resetRuleForm() {
      this.ruleForm = {
        ticketNo: '',
        ticketName: '',
        calculateType: 'NO_STEP',
        accumulationTime: ['', ''],
        validityTime: ['', ''],
      }
    },
    // 清理table数据
    resetFeeTableData() {
      this.tableData = []
      this.tableEditValRules = {}
    },
    /**
     * @description 单元格变更
     * @param { number } index 第几行
     * @param { string } column 第几列
     * @param data 数据
     * @param { boolean } isVal 是否校验
     * @param { CellOptions } options 配置选项
     */
    onCellChange(index: number, column: string, data: any, isVal: boolean = true, options: CellOptions = { valueType: 'primitive' }) {
      let _data
      if (data && isObject(data)) {
        _data = JSON.parse(JSON.stringify(data))
      } else if (isNumber(data)) {
        _data = data
      } else {
        _data = data || ''
      }
      if (options.valueType === 'primitive') {
        this.tableData[index][column] = JSON.parse(JSON.stringify(_data))
        // 执行副作用
        if (options.callback) {
          options.callback(index, column, _data, 'primitive')
        }
      } else if (options.valueType === 'object') {
        this.tableData[index][column][options.objectConfig ? options.objectConfig.field : ''] = _data
        // 执行副作用
        if (options.callback) {
          options.callback(index, column, _data, 'object', options.objectConfig && options.objectConfig.field)
        }
        // ----副作用----
        // 如果退款按比例退款，需要将计算类型字段相关字段移除
        const _field = options.objectConfig && options.objectConfig.field
        if (column === 'REFUND' && _field === 'refundByProportion') {
          // 如果是Y
          if (_data === 'Y') {
            // 需要将计算类型字段相关字段移除
            // 移除值
            this.tableData[index].REFUND.value = ''
            this.tableData[index].REFUND.calFormula = ''

            // 移除元素
            // 拿到PAY_object 下的elements，倒序移除
            const elements = this.tableData[index].REFUND__object.elements

            // 如果是组合计费
            if (this.ruleForm.calculateType === 'COMBINED_BILLING') {
              this.tableData[index].REFUND.calFormula = ''
              // 找到计算公式，并移除
              const calFormulaIndex = elements.findIndex((ele: any) => ele.dataIndex === 'calFormula')
              calFormulaIndex > 0 && elements.splice(calFormulaIndex, 1)
            }

            // 找到value字段，并移除
            const valueIndex = elements.findIndex((ele: any) => ele.dataIndex === 'value')
            valueIndex > 0 && elements.splice(valueIndex, 1)
            // 找到calConfigRule字段，并移除
            const calConfigRuleIndex = elements.findIndex((ele: any) => ele.dataIndex === 'calConfigRule')
            calConfigRuleIndex > 0 && elements.splice(calConfigRuleIndex, 1)
          } else if (_data === 'N') {
            // 需要将计算类型字段相关加入
            // 初始化赋值
            this.tableData[index].REFUND.calConfigRule = 'FIXED'
            this.tableData[index].REFUND.value = ''

            // 添加元素
            // 拿到PAY_object 下的elements
            const elements = this.tableData[index].REFUND__object.elements
            // 找到refundByProportion字段，需要确保添加顺序
            const refundByProportionIndex = elements.findIndex((ele: any) => ele.dataIndex === 'refundByProportion')
            // 找到calConfigRule字段，并加入
            const calConfigRuleIndex = elements.findIndex((ele: any) => ele.dataIndex === 'calConfigRule')
            !(calConfigRuleIndex > 0) && elements.splice(refundByProportionIndex + 1, 0, this.tableObjectElementMap.calConfigElementRequired)
            // 找到value字段，并加入
            const valueIndex = elements.findIndex((ele: any) => ele.dataIndex === 'value')
            !(valueIndex > 0) && elements.splice(refundByProportionIndex + 2, 0, {
              ...this.tableObjectElementMap.valueElementRequired,
              ...this.getCalConfigRuleMaxAndTitle(this.tableData[index].REFUND.calConfigRule)
            })
            // 如果是组合计费
            if (this.ruleForm.calculateType === 'COMBINED_BILLING') {
              this.tableData[index].REFUND.calFormula = ''
              // 找到计算公式，并加入
              const calFormulaIndex = elements.findIndex((ele: any) => ele.dataIndex === 'calFormula')
              !(calFormulaIndex > 0) && elements.splice(refundByProportionIndex + 3, 0, this.tableObjectElementMap.calFormulaElementRequired)
            }
          }
        } else if (['PAY', 'REFUND'].includes(column) && _field === 'calConfigRule') {
          // 如果是配置规则的变更，需要动态改value的配置
          // TODO: 需要动态改value的配置
          const _calConfigRuleValue = this.tableData[index][column][_field]
          const elements = JSON.parse(JSON.stringify(this.tableData[index][column + '__object'].elements || []))
          if (_calConfigRuleValue === 'FIXED') {
            // 如果是固定金额，则需要将value的值乘以100
            // 拿到elements
            const valueIndex = elements.findIndex((ele: any) => ele.dataIndex === 'value')
            const value = elements[valueIndex]
            value.max = 99999.9999
            value.title = '配置值(元)'
            elements[valueIndex] = value
            this.tableData[index][column + '__object'].elements = elements
          } else if (_calConfigRuleValue === 'RATE') {
            // 如果是费率，则需要将value的值除以100
            const valueIndex = elements.findIndex((ele: any) => ele.dataIndex === 'value')
            const value = elements[valueIndex]
            value.max = 100
            value.title = '配置值(%)'
            elements[valueIndex] = value
            this.tableData[index][column + '__object'].elements = elements
          }
          // 设置value的值为''
          // 这里直接改值不会触发校验
          this.tableData[index][column].value = ''
        }

        if (['REFUND'].includes(column) && this.ruleForm.calculateType === 'COMBINED_BILLING' && _field && ['calConfigRule', 'value'].includes(_field)) {
          // 单独逻辑 支付、退款 + 组合计费
          // 如果变更的是calConfigRule/value
          // 如果calConfigRule/value同时有值,移除calFormula的必填标记
          const refund = this.tableData[index].REFUND
          const elements = this.tableData[index].REFUND__object.elements
          elements.find((ele: any) => ele.dataIndex === 'calFormula').required = !(refund.calConfigRule && isNumber(refund.value))
        } else if (['REFUND'].includes(column) && this.ruleForm.calculateType === 'COMBINED_BILLING' && _field && ['calFormula'].includes(_field)) {
          // 单独逻辑 支付 + 组合计费
          // 如果变更的是calConfigRule/value
          // 如果calConfigRule/value同时有值,移除calFormula的必填标记
          const refund = this.tableData[index].REFUND
          const elements = this.tableData[index].REFUND__object.elements
          if (refund.calFormula) {
            elements.find((ele: any) => ele.dataIndex === 'calConfigRule').required = false
            elements.find((ele: any) => ele.dataIndex === 'value').required = false
          } else {
            elements.find((ele: any) => ele.dataIndex === 'calConfigRule').required = true
            elements.find((ele: any) => ele.dataIndex === 'value').required = true
          }
        }
      }
      // 找到每一列的规则
      // 根据值类型，找到对应的校验规则
      let validator: boolean | Validator = false
      if (options.valueType === 'primitive') {
        validator = this.tableEditValRules[column] ? this.tableEditValRules[column].validator : false
        if (validator !== false) {
          this.onCellValidate(index, column, _data, validator, options)
        }
      } else if (options.valueType === 'object') {
        const _field = options.objectConfig ? options.objectConfig.field : ''
        validator = this.tableEditValRules[_field] ? this.tableEditValRules[_field].validator : false
        // 如果当前字段需要校验，则校验，内部会判断对象整体需不需要
        if (validator !== false) {
          this.onCellValidate(index, column, _data, validator, options)
        } else {
          // 如果当前字段不需要校验，则判断对象整体需不需要
          if (this.tableEditValRules[column] && this.tableEditValRules[column].validator !== false) {
            this.onCellValidate(index, column, this.tableData[index][column], this.tableEditValRules[column].validator, options = { valueType: 'primitive' })
          }
        }
      }
    },

    onCellValidate(index: number, column: string, data: any, validator: boolean | Validator, options: CellOptions = { valueType: 'primitive' }): boolean {
      let hasErr = false
      let _validator = validator
      if (options.valueType === 'primitive') {
        // 如果是boolean类型，定义validator为通用必填校验规则
        if (typeof validator === 'boolean') {
          if (validator) {
            _validator = (value: any) => {
            // 校验必填
            // 校验通过返回空字符串，校验不通过返回错误信息
              if (Array.isArray(value) && value.length === 0) {
                return this.tableEditValRule[column].message || '必填'
              }
              if (value === '' || value === null || value === undefined) {
                return this.tableEditValRule[column].message || '必填'
              }
              return ''
            }
          } else {
            // 如果设置为不校验，则清空错误信息
            this.$set(this.tableData[index], column + '__err_msg', '')
          }
        }

        // 如果是函数类型，直接调用函数。归一化，到这里一定是函数
        if (typeof _validator === 'function') {
          const result = _validator(index, column, data, 'primitive')
          if (result) {
            // 校验不通过
            hasErr = true
            // 使用$set，否则无法触发视图更新
            this.$set(this.tableData[index], column + '__err_msg', result)
          } else {
            // 校验通过
            hasErr = false
            this.$set(this.tableData[index], column + '__err_msg', '')
          }
        }
      } else if (options.valueType === 'object') { // 如果是object类型，则校验对象中的每个字段
        if (typeof validator === 'boolean') {
          if (validator) {
            _validator = (index: number, column: string, data: any, valueType: string, objectField?: string) => {
            // 校验必填
            // 校验通过返回空字符串，校验不通过返回错误信息
              if (Array.isArray(data) && data.length === 0) {
                return '必填'
              }
              if (data === '' || data === null || data === undefined) {
                return '必填'
              }
              return ''
            }
          } else {
          // 如果设置为不校验，则清空错误信息
            options.objectConfig && this.$set(this.tableData[index][column], options.objectConfig.field + '__err_msg', '')
          }
        }

        // 如果是函数类型，直接调用函数。归一化，到这里一定是函数
        if (typeof _validator === 'function') {
          const result = _validator(index, column, data, 'object', options.objectConfig && options.objectConfig.field)
          if (result) {
          // 校验不通过
            hasErr = true
            const _data = JSON.parse(JSON.stringify(this.tableData[index][column]))
            options.objectConfig && this.$set(_data, options.objectConfig.field + '__err_msg', result)
            if (options.objectConfig) {
              this.tableData[index][column] = {
                ..._data,
                [options.objectConfig.field + '__err_msg']: result
              }
            }
          } else {
          // 校验通过
            hasErr = false
            const _data = JSON.parse(JSON.stringify(this.tableData[index][column]))
            options.objectConfig && this.$set(_data, options.objectConfig.field + '__err_msg', '')
            if (options.objectConfig) {
              this.tableData[index][column] = {
                ..._data,
                [options.objectConfig.field + '__err_msg']: ''
              }
            }
          }
        }
        // 如果这个对象需要整体校验
        if (this.tableEditValRules[column]) {
          let _hasErr = false
          _hasErr = this.onCellValidate(index, column, this.tableData[index][column], this.tableEditValRules[column].validator, options = { valueType: 'primitive' })
          hasErr = hasErr || _hasErr
        }
      }
      return hasErr
    },

    // 渠道手续费整体校验
    onValidateAll(): boolean {
      let hasErr = false
      // 遍历tableData，对每一行进行校验
      this.tableData.forEach((row: any, index: number) => {
        Object.entries(this.tableEditValRules).forEach(([key, rule]: [string, any]) => {
          if (rule.fieldType === 'object') {
            const options: CellOptions = { valueType: 'object', objectConfig: { field: key } }
            rule.parentKeys && rule.parentKeys.forEach((parentKey: string) => {
              hasErr = this.onCellValidate(index, parentKey, row[parentKey][key], rule.validator, options) || hasErr
            })
          } else {
            hasErr = this.onCellValidate(index, key, row[key], rule.validator, { valueType: 'primitive' }) || hasErr
          }
        })
      })
      return hasErr
    },

    // 新增交易区间
    // 也需要考虑清算机构，只负责追加，如果tableData有数据，则追加，否则覆盖
    handleAddTradeRange() {
      const tradeRange = {
        amountStart: undefined,
        amountEnd: undefined
      }
      const _tableData: CalFeeRuleDisplay[] = []
      // 需要考虑的清算机构 银联网联
      if (this.feeTypeNum > 1) {
        const _feeTypeEnum = this.routeBaseInfo.feeTypes.filter((item: EnumBase) => item.key !== 'ALL')
        _feeTypeEnum.forEach((feeType: EnumBase) => {
          // 设置一条支付
          const payRule: CalFeeRule = this.getDefaultCalFeeRuleByCalFeeType('PAY', this.ruleForm.calculateType, feeType.key)
          // 设置一条退款
          const refundRule: CalFeeRule = this.getDefaultCalFeeRuleByCalFeeType('REFUND', this.ruleForm.calculateType, feeType.key)
          // 定义展示数据
          const _tableDataDisplay: CalFeeRuleDisplay = {} as CalFeeRuleDisplay
          this.$set(_tableDataDisplay, 'id', this.getNewId())
          this.$set(_tableDataDisplay, 'tradeRange', tradeRange)
          this.$set(_tableDataDisplay, 'PAY', payRule)
          this.$set(_tableDataDisplay, 'REFUND', refundRule)
          // 设置展示列
          this.$set(_tableDataDisplay, 'tradeRange__object', {
            elements: [
              this.tableObjectElementMap.tradeAmountStartElementRequired,
              this.tableObjectElementMap.tradeAmountEndElementRequired
            ]
          })
          this.$set(_tableDataDisplay, 'PAY__object', {
            elements: [
              this.tableObjectElementMap.calConfigElementRequired,
              {
                ...this.tableObjectElementMap.valueElementRequired,
                ...this.getCalConfigRuleMaxAndTitle(payRule.calConfigRule),
              },
              this.tableObjectElementMap.minValueElement,
              this.tableObjectElementMap.maxValueElement
            ]
          })
          this.$set(_tableDataDisplay, 'REFUND__object', {
            elements: [
              this.tableObjectElementMap.refundByProportionElementRequired,
              ...(refundRule.refundByProportion === 'N' ? [
                this.tableObjectElementMap.calConfigElementRequired,
                {
                  ...this.tableObjectElementMap.valueElementRequired,
                  ...this.getCalConfigRuleMaxAndTitle(refundRule.calConfigRule),
                },
              ] : []),
              this.tableObjectElementMap.minValueElement,
              this.tableObjectElementMap.maxValueElement
            ]
          })
          this.$set(_tableDataDisplay, 'feeType', feeType.key)
          this.$set(_tableDataDisplay, 'feeType__desc', getLabelFromEnum(feeType.key, this.feeTypeEnum, ['value', 'key']))
          _tableData.push(_tableDataDisplay)
        })
      } else { // 其他机构 单条追加
        // 设置一条支付
        const payRule: CalFeeRule = this.getDefaultCalFeeRuleByCalFeeType('PAY', this.ruleForm.calculateType, '')
        // 设置一条退款
        const refundRule: CalFeeRule = this.getDefaultCalFeeRuleByCalFeeType('REFUND', this.ruleForm.calculateType, '')
        // 定义展示数据
        const _tableDataDisplay: CalFeeRuleDisplay = {} as CalFeeRuleDisplay
        this.$set(_tableDataDisplay, 'id', this.getNewId())
        this.$set(_tableDataDisplay, 'tradeRange', tradeRange)
        this.$set(_tableDataDisplay, 'tradeRange__object', {
          elements: [
            this.tableObjectElementMap.tradeAmountStartElementRequired,
            this.tableObjectElementMap.tradeAmountEndElementRequired
          ]
        })
        this.$set(_tableDataDisplay, 'PAY', payRule)
        this.$set(_tableDataDisplay, 'REFUND', refundRule)
        this.$set(_tableDataDisplay, 'PAY__object', {
          elements: [
            this.tableObjectElementMap.calConfigElementRequired,
            {
              ...this.tableObjectElementMap.valueElementRequired,
              ...this.getCalConfigRuleMaxAndTitle(payRule.calConfigRule),
            },
            this.tableObjectElementMap.minValueElement,
            this.tableObjectElementMap.maxValueElement
          ]
        })
        this.$set(_tableDataDisplay, 'REFUND__object', {
          elements: [
            this.tableObjectElementMap.refundByProportionElementRequired,
            ...(refundRule.refundByProportion === 'N' ? [
              this.tableObjectElementMap.calConfigElementRequired,
              {
                ...this.tableObjectElementMap.valueElementRequired,
                ...this.getCalConfigRuleMaxAndTitle(refundRule.calConfigRule),
              },
            ] : []),
            this.tableObjectElementMap.minValueElement,
            this.tableObjectElementMap.maxValueElement
          ]
        })
        _tableData.push(_tableDataDisplay)
      }
      // 追加完加入tableData
      if (this.tableData.length > 0) {
        _tableData.forEach((item: CalFeeRuleDisplay) => {
          this.tableData.push(item)
        })
      } else {
        this.tableData = _tableData
      }
    },
    // 删除交易区间
    onRowDel(index: number) {
      // 根据xxx类型进行删除1个还是删除3个
      if (this.feeTypeNum > 1) {
        this.tableData.length > this.feeTypeNum ? this.tableData.splice(index, this.feeTypeNum) : this.$message.warning('至少一组费用规则')
      } else {
        this.tableData.length > 1 ? this.tableData.splice(index, 1) : this.$message.warning('至少一条费用规则')
      }
    },
    // 手动输入计费元素输入时
    handleCalItemInput(ele: string, value: string) {
      this.$set(this.addForm, ele, value)
    },
    handleResetAddForm() {
      this.addForm = {}
    },
    // 手动新增计费元素
    handleAddCalItem(ele: string) {
      // 数据源增加
      // 目标选项增加
      if (this.curOperateEle[ele].dataSource.some(item => item.key === this.addForm[ele])) {
        this.$message.warning(`已存在${getLabelFromEnum(ele, this.calculateItemEnum, ['value', 'key'])}: ${this.addForm[ele]}`)
        return
      }
      this.curOperateEle[ele].dataSource.push({
        title: this.addForm[ele],
        key: this.addForm[ele]
      })
      this.curOperateEle[ele].targetKeys.push(this.addForm[ele])
    },
    // 处理计费类型变化
    handleCalculateTypeChange(value: string) {
      if (this.routeBaseInfo.clearingOrgCode && value) {
        this.initRuleTableValRules()
        // 先重置
        this.tableData = []
        this.prepareRuleData([], this.routeBaseInfo.clearingOrgCode, value, this.feeTypeNum)
      }
    },
    // 根据计费类型，转义规则/可视化计费规则
    handleVisualizeRule(calRule: CalFeeRule): string {
      // 无阶梯、单笔阶梯、累计阶梯的可视化方式一样不过有支付和退款的区别
      const calFeeType = calRule.calFeeType
      if (calRule.refundByProportion === 'Y') {
        return '--'
      }
      if (calFeeType === 'COMBINED_BILLING') {
        if (calRule.tradeType === 'PAY') {
          return calRule.calFormula || '--'
        }
        if (calRule.tradeType === 'REFUND' && calRule.calFormula) {
          return calRule.calFormula || '--'
        }
      }
      const calConfigRuleEnum = this.calConfigRuleEnum.find((e: EnumBase) => e.key === calRule.calConfigRule) || {} as EnumBase
      // 处理值为千分符
      let thoundValue = '--'
      if (isNumber(calRule.value)) {
        thoundValue = toThousands(Number(calRule.value), 4)
      }
      return `${calConfigRuleEnum.value || calRule.calConfigRule || '--'} = ${thoundValue} ${ units[calRule.calConfigRule] || ''}`
    },
    /**
     * @description 检查区间是否重叠
     * @param {Array} ranges 区间数组
     * @returns {Object} 返回一个对象，包含hasOverlap属性，表示是否存在重叠区间，以及overlappingRanges属性，表示重叠的区间
     */
    hasOverlap(ranges: TradeRange[]) {
      // 按起始值排序
      const sorted = [...ranges].sort((a, b) => a.amountStart - b.amountStart)

      // 检查相邻区间
      for (let i = 0; i < sorted.length - 1; i++) {
        if (sorted[i].amountEnd > sorted[i + 1].amountStart) {
          return {
            hasOverlap: true,
            overlappingRanges: [sorted[i], sorted[i + 1]]
          }
        }
      }

      return { hasOverlap: false }
    },
    // 重置穿梭框搜索框
    resetSearch() {
      // 获取 Transfer 组件的 DOM 元素
      const transferElements: any[] = this.$refs.transfer
      if (Array.isArray(transferElements)) {
        transferElements.forEach((transferElement: any) => {
          const ele = transferElement.$el
          // 查找左侧和右侧的搜索框
          const searchInput = ele.querySelectorAll('.ant-transfer-list-search')
          searchInput.forEach((item: any) => {
            item.value = ''
            item.dispatchEvent(new Event('input'))
          })
        })
      }
    },

    // 过滤表单查询
    handleFilter() {
      this.displayPage = 1
      // 过滤路由列表
      const _routeListDisplay = this.routeListDisplayAll.filter((route: RouteListDisplay) => {
        // 遍历过滤条件
        return Object.entries(this.filterForm).every(([key, value]) => {
          // 如果过滤值为空,则不过滤该条件
          if (!value || (Array.isArray(value) && value.length === 0)) {
            return true
          }
          // 获取路由中对应的键值对
          const routeKeyValue = route.calRuleDetail.find(item => item.key === key)
          // 如果键值对不存在 或者 键值对的值为空，则不过滤该条件
          if (!routeKeyValue || !routeKeyValue.value) {
            return true
          }
          // 如果键值对的值为数组，则需要遍历数组
          if (Array.isArray(routeKeyValue.value)) {
            // 是不是特殊计费元素
            if (this.SpecialCalEles.includes(key)) {
              // 特殊计费元素，value是字符串时模糊匹配
              return routeKeyValue.value.includes(value as string)
            } else {
              // 非特殊计费元素，数组完全匹配
              return (value as string[]).some(v => routeKeyValue.value.includes(v))
            }
          } else {
            // 业务逻辑，只能是数组
            return true
          }
        })
      })
      this.routeListDisplay = clone(_routeListDisplay)
    },
    // 过滤表单重置
    handleFilterReset() {
      this.$refs.filterFormRef.resetFields()
      this.routeListDisplay = clone(this.routeListDisplayAll)
    },
    filterOption(input: string, option: any) {
      return (
        option.componentOptions.children[0].text.toLowerCase().indexOf(input.toLowerCase()) >= 0
      )
    },
  }
})
</script>
<template>
  <div class="p-5">
    <div class="px-16px">
      <!-- 基本信息 -->
      <a-descriptions title="基本信息">
        <a-descriptions-item label="清算机构">
          {{ getLabelFromEnum(routeBaseInfo.clearingOrgCode, clearingOrgEnum, ['value', 'key']) || '--' }}
        </a-descriptions-item>
        <a-descriptions-item v-if="routeBaseInfo.clearingOrgCode !== 'OTHER'" label="对账接口">
          {{ getLabelFromEnum(routeBaseInfo.checkCode, checkCodesAndItService, ['checkName', 'checkCode']) || '--' }}
        </a-descriptions-item>
        <a-descriptions-item v-else label="技术服务商">
          {{ routeBaseInfo.itServiceVendor || '--' }}
        </a-descriptions-item>
        <a-descriptions-item label="结算方式">
          {{ getLabelFromEnum(routeBaseInfo.settlementType, settlementTypeEnum, ['value', 'key']) || '--' }}
        </a-descriptions-item>
        <a-descriptions-item v-if="routeBaseInfo.clearingOrgCode !== 'OTHER'" label="默认计费规则" :span="3">
          {{ getLabelFromEnum(routeBaseInfo.parentId, commonRules, ['ruleName', 'id']) || '--' }}
        </a-descriptions-item>
        <a-descriptions-item label="计费优先级" :span="3">
          {{ getLabelFromEnum(routeBaseInfo.calculatePrecedenceId, calEleGroup, ['calculatePrecedencesDesc', 'id']) || '--' }}
        </a-descriptions-item>
        <a-descriptions-item label="生效期间" :span="2">
          {{ routeBaseInfo.validityStartTime || '--' }} ~ {{ routeBaseInfo.validityEndTime || '--' }}
        </a-descriptions-item>
      </a-descriptions>

      <a-divider />

      <!-- 过滤面板 -->
      <a-form-model ref="filterFormRef" class="filter-form" :model="filterForm" :label-col="{ span: 8 }" :wrapper-col="{ span: 16 }">
        <a-row :gutter="[8, 8]">
          <a-col v-for="(element) in elementList" :key="element" :sm="12" :md="8" :lg="6">
            <a-form-model-item v-if="SpecialCalEles.includes(element)" :prop="element" :label="getLabelFromEnum(element, calculateItemEnum, ['value', 'key'])">
              <a-input
                v-model="filterForm[element]"
                size="small"
                :placeholder="`请输入${getLabelFromEnum(element, calculateItemEnum, ['value', 'key'])}`"
                allowClear
              />
            </a-form-model-item>
            <a-form-model-item v-else :prop="element" :label="getLabelFromEnum(element, calculateItemEnum, ['value', 'key'])">
              <a-select
                v-model="filterForm[element]"
                size="small"
                mode="multiple"
                :placeholder="`请选择${getLabelFromEnum(element, calculateItemEnum, ['value', 'key'])}`"
                :show-search="true"
                allowClear
                :filter-option="filterOption"
              >
                <a-select-option v-for="item in calculateItemEnumMap[element]" :key="item.key" :value="item.key">
                  {{ item.value }}
                </a-select-option>
              </a-select>
            </a-form-model-item>
          </a-col>
          <a-col :sm="24 - (elementList.length % 2) * 12" :md="24 - (elementList.length % 3) * 8" :lg="24 - (elementList.length % 4) * 6">
            <a-form-model-item :wrapper-col="{ span: 24 }">
              <div class="w-full flex items-center justify-end h-40px!">
                <a-button type="primary" size="small" class="mr-8px" @click="handleFilter">查询</a-button>
                <a-button size="small" @click="handleFilterReset">重置</a-button>
              </div>
            </a-form-model-item>
          </a-col>
        </a-row>
      </a-form-model>

      <!-- 路由规则列表 -->
      <!-- 头部操作区域 -->
      <div class="table-operation-container">
        <div class="table-operation-left">
          <!-- title -->
          <div class="color-black font-bold text-16px">
            {{ '规则列表' }}
          </div>
          <!-- 描述 -->
          <div>
          </div>
        </div>
        <div class="table-operation-right">
          <a-button type="primary" @click="handleAddPrecedenceModal">
            新增计费优先级
          </a-button>
        </div>
      </div>
      <a-table
        :columns="dynamicRoutesColumns"
        :data-source="routeListDisplay"
        class="components-table-demo-nested"
        size="small"
        :pagination="{
          current: displayPage,
          pageSize: pageSize,
          showSizeChanger: true,
          pageSizeOptions: ['10', '20', '50', '100'],
          showTotal: (total, range) => `共 ${total} 条记录 第 ${displayPage} / ${Math.ceil(total / pageSize)} 页`,
          onChange: handlePageChange
        }"
        :row-key="'id'"
        :scroll="{ x: dynamicRoutesColumnsWidth }"
        :expandedRowKeys="expandedRowKeys"
        @expand="handleExpand"
      >
        <template v-for="ele in elementList" :slot="ele" slot-scope="text">
          <div :key="ele" class="flex flex-wrap w-full">
            <a-tag
              v-for="item in text.slice(0, 5)"
              :key="item"
              :color="filterForm[ele].includes(item) ? 'green' : ''"
              :bordered="false"
              class="mb-4px!">
              {{ SpecialCalEles.includes(ele) ? shortenString(item, 16) : shortenString(`${getLabelFromEnum(item, calculateItemEnumMap[ele], ['value', 'key'])}`,10) }}
            </a-tag>
            <a-tooltip v-if="text.length > 5">
              <div class="text-12px! opacity-80 cursor-pointer">
                {{ `+${+text.length - 5}` }}
              </div>
              <template slot="title">
                <div class="max-h-300px! overflow-y-auto">
                  <a-tag
                    v-for="item in text"
                    :key="item"
                    :color="filterForm[ele].includes(item) ? 'green' : ''"
                    :bordered="false"
                    class="mb-4px!">
                    {{ SpecialCalEles.includes(ele) ? item : `${getLabelFromEnum(item, calculateItemEnumMap[ele], ['value', 'key'])}` }}
                  </a-tag>
                </div>
              </template>
            </a-tooltip>
          </div>
        </template>
        <!-- 操作列 -->
        <template slot="operation" slot-scope="text,record">
          <a-button type="link" class="pl-0!" @click="() => { handleEditPrecedenceModal(record) }">编辑计费优先级</a-button>
          <a-button type="link" class="pl-0!" @click="() => { handleAddRuleModal(record) }">新增计费规则</a-button>
        </template>
        <!-- 子表格 -->
        <a-table
          slot="expandedRowRender"
          slot-scope="record"
          class="mt-8px! mb-12px!"
          :columns="calRuleColumns"
          :data-source="record.expandedRowRender"
          :pagination="false"
          :row-key="'id'"
          size="small"
          :scroll="{ x: dynamicChirldWidth }"
        >
          <!-- 交易类型 -->
          <template slot="tradeType" slot-scope="_text">
            {{ getLabelFromEnum(_text, tradeTypeEnum, ['value', 'key']) || '--' }}
          </template>
          <!-- 计费类型 -->
          <template slot="calFeeType" slot-scope="_text">
            {{ getLabelFromEnum(_text, calculateTypeEnum, ['value', 'key']) || '--' }}
          </template>
          <!-- 费用类型 -->
          <template slot="feeType" slot-scope="_text">
            {{ _text !== 'COMMON' ? (getLabelFromEnum(_text, feeTypeEnum, ['value', 'key']) || '--') : '--' }}
          </template>
          <!-- 按比例退款 -->
          <template slot="refundByProportion" slot-scope="_text">
            {{ getLabelFromEnum(_text, yNEnum, ['value', 'key']) || '--' }}
          </template>
          <!-- 交易区间 -->
          <template slot="tradeRange" slot-scope="_text">
            {{
              isNumber(_text.amountStart) ?
                `${_text.amountStart} ~  ${_text.amountEnd}`
                : '--'
            }}
          </template>
          <!-- 日期 -->
          <template slot="validityTimeRange" slot-scope="_text">
            {{ _text.validityStartTime || '--' }} <br> {{ _text.validityEndTime || '--' }}
          </template>
          <!-- 操作 -->
          <span slot="operation" slot-scope="_text,_record" class="table-operation">
            <a-button type="link" class="pl-0!" @click="() => { handleEditRuleModal(_record.calRuleId, record) }">编辑计费规则</a-button>
          </span>
        </a-table>
      </a-table>

      <!-- 计费元素弹窗 -->
      <a-drawer
        :visible="precedenceVisible"
        :title="`${curModalType === ModalTypeEnum.ADD ? '新增' : '编辑'}计费优先级`"
        width="800"
        class="precedence-drawer"
        @close="onClosePrecedenceModal"
      >
        <a-spin :spinning="calElementSubmiting">
          <a-form-model ref="precedenceForm" :model="precedenceForm" v-bind="layout">
            <a-form-model-item label="计费优先级" prop="ruleOrder" :rules="{ required: true, message: '计费优先级不能为空' }">
              <a-input-number v-model="precedenceForm.ruleOrder" :min="0" :precision="0" />
            </a-form-model-item>
            <a-collapse accordion>
              <!-- key可以根据计费元素列表动态顺序 -->
              <a-collapse-panel v-for="ele in elementList" :key="ele">
                <template #header>
                  {{ `${getLabelFromEnum(ele, calculateItemEnum, ['value', 'key'])}: ${ele}` }}
                </template>
                <!-- 如果枚举是 MERCHANT_NO/INDUSTRY_CODE 需要额外增加一个添加的输入框 -->
                <div v-if="SpecialCalEles.includes(ele)" class="add-item flex mb-12px!">
                  <a-input
                    v-model="addForm[ele]"
                    class="w-220px!"
                    :placeholder="`请输入${getLabelFromEnum(ele, calculateItemEnum, ['value', 'key'])}`"
                    :max-length="50"
                    @change="(e) => handleCalItemInput(ele, e.target.value)"
                  />
                  <a-button
                    type="link"
                    icon="plus"
                    class="ml-8px! pl-0!"
                    :disabled="!addForm[ele]"
                    @click="() => handleAddCalItem(ele)"
                  >
                    添加
                  </a-button>
                </div>
                <a-transfer
                  ref="transfer"
                  :data-source="curOperateEle[ele] && curOperateEle[ele].dataSource"
                  show-search
                  :titles="['待选', '已选']"
                  :list-style="{
                    width: '250px',
                    height: '350px',
                  }"
                  :lazy="false"
                  :target-keys="curOperateEle[ele] && curOperateEle[ele].targetKeys"
                  :render="item => SpecialCalEles.includes(ele) ? item.title : `${item.title}(${item.key})`"
                  @change="(nextTargetKeys, direction, moveKeys) => handleChange(nextTargetKeys, direction, moveKeys, ele)"
                >
                </a-transfer>
              </a-collapse-panel>
            </a-collapse>
          </a-form-model>
          <div
            :style="{
              position: 'absolute',
              right: 0,
              bottom: 0,
              width: '100%',
              borderTop: '1px solid #e9e9e9',
              padding: '10px 16px',
              background: '#fff',
              textAlign: 'right',
              zIndex: 1,
            }"
          >
            <!-- <a-button :style="{ marginRight: '8px' }" @click="onClose"> -->
            <a-button :style="{ marginRight: '8px' }" @click="onClosePrecedenceModal">
              取消
            </a-button>
            <a-button
              type="primary"
              @click="submitPrecedenceModal"
            >
              确认
            </a-button>
          </div>
        </a-spin>
      </a-drawer>

      <!-- 编辑计费规则弹窗 -->
      <a-drawer
        :visible="ruleVisible"
        :title="`${curModalType === ModalTypeEnum.ADD ? '新增' : '编辑'}计费规则`"
        width="80%"
        class="rule-drawer"
        @close="onCloseRuleModal"
      >
        <a-spin :spinning="ruleDrawerSubmiting">
          <a-form-model ref="ruleForm" :model="ruleForm" v-bind="layout">
            <a-form-model-item label="OP工单编号" prop="ticketNo" :rules="[{ required: true, message: 'OP工单编号不能为空' }, { max: 100, message: 'OP工单编号不能超过100个字符' }]">
              <a-input v-model="ruleForm.ticketNo" :disabled="curModalType === ModalTypeEnum.EDIT"/>
            </a-form-model-item>
            <a-form-model-item label="OP工单名称" prop="ticketName" :rules="[{ required: true, message: 'OP工单名称不能为空' }, { max: 100, message: 'OP工单名称不能超过100个字符' }]">
              <a-input v-model="ruleForm.ticketName" :disabled="curModalType === ModalTypeEnum.EDIT" />
            </a-form-model-item>
            <a-form-model-item label="计费类型" prop="calculateType" :rules="{ required: true, message: '计费类型不能为空' }">
              <a-radio-group v-model="ruleForm.calculateType" :disabled="curModalType === ModalTypeEnum.EDIT" @change="(e) => handleCalculateTypeChange(e.target.value)">
                <!-- 分期和组合计费暂时禁用 -->
                <a-radio
                  v-for="item in calculateTypeEnum"
                  :key="item.key"
                  :value="item.key"
                  :disabled="['CUMULATIVE_STEP', 'INSTALLMENT'].includes(item.key)"
                >
                  {{ item.value }}
                </a-radio>
              </a-radio-group>
            </a-form-model-item>
            <!-- 生效期间 -->
            <a-form-model-item label="生效期间" prop="validityTime" :rules="[{ type: 'array', required: true, validator: validateTimeRange }]">
              <a-range-picker
                v-model="ruleForm.validityTime"
                :show-time="{
                  hideDisabledOptions: true,
                }"
                :disabled-date="disabledDate"
                value-format="YYYY-MM-DD HH:mm:ss"
                format="YYYY-MM-DD HH:mm:ss"
                :placeholder="['开始时间', '结束时间']"
              />
            </a-form-model-item>
            <a-form-model-item label="渠道手续费配置" :wrapperCol="{ span: 24 }">
              <a-button
                v-show="ruleForm.calculateType==='SINGLE_STEP'"
                type="link"
                icon="plus"
                @click="handleAddTradeRange"
              >
                新增交易区间
              </a-button>
              <rule-item
                :columns="feeColumns"
                :dataSource="tableData"
                :pagination="false"
                :rules="tableEditValRules"
                :rowKey="'id'"
                bordered
                size="small"
                @cellChange="onCellChange"
                @rowDel="onRowDel"
              />
            </a-form-model-item>
          </a-form-model>

          <div
            :style="{
              position: 'absolute',
              right: 0,
              bottom: 0,
              width: '100%',
              borderTop: '1px solid #e9e9e9',
              padding: '10px 16px',
              background: '#fff',
              textAlign: 'right',
              zIndex: 1,
            }"
          >
            <a-button
              :style="{ marginRight: '8px' }"
              @click="onCloseRuleModal"
            >
              取消
            </a-button>
            <a-button
              type="primary"
              @click="onSubmitRuleModal"
            >
              确认
            </a-button>
          </div>
        </a-spin>
      </a-drawer>
    </div>
  </div>
</template>

<style lang="less" scoped>
.p-5 {
  ::v-deep .ant-table-body {
    margin: 0 !important;
  }
}
.precedence-drawer, .rule-drawer {
  ::v-deep .ant-drawer-body {
    padding-bottom: 54px !important;
  }
  ::v-deep .ant-spin-nested-loading {
    position: inherit !important;
  }
  ::v-deep .ant-spin-container {
    position: inherit !important;
  }
}

.filter-form{
  ::v-deep .ant-form-item{
    margin-bottom: 0;
  }
  ::v-deep .ant-form-item-label {
    line-height: 26px !important;
  }
  ::v-deep .ant-form-item-control{
    line-height: 26px !important;
  }
}

.rule-drawer {
  ::v-deep .ant-form-item {
    margin-bottom: 0;
  }
  ::v-deep .ant-table-body {
    margin: 0 !important;
  }
  ::v-deep .ant-table-tbody > tr:hover:not(.ant-table-expanded-row):not(.ant-table-row-selected) > td {
    background: transparent;
  }
  ::v-deep .ant-spin-nested-loading {
    position: inherit !important;
  }
  ::v-deep .ant-spin-container {
    position: inherit !important;
  }
}
.table-operation-container{
  display: flex;
  justify-content: flex-end;
  width: 100%;
  margin-bottom: 12px;
  .table-operation-left{
    flex: 1;
    justify-content: flex-start;
  }
  .table-operation-right{
    flex: 0;
  }
}

::v-deep .ant-table-scroll .ant-table-body{
  overflow-x: auto !important;
}
::v-deep .ant-table-pagination{
  width: 100%;
  display: flex;
}
::v-deep .ant-pagination-total-text{
  flex-grow: 1;
}
</style>
