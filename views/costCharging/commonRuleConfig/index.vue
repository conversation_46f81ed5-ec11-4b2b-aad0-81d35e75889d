<script lang="ts">
import { defineComponent } from '@vue/composition-api'
import { fetchSchema } from '@/utils/lowcode'
import { CalFeeRule, CalFeeRuleDetail, CalFeeRuleDisplay } from '_/types/costCharging'
import { EnumBase } from '_/types/enums/enums'
import { getLabelFromEnum, getUid } from '_/utils'
import RuleItem from './RuleItem/index.vue'
import { CellOptions } from './RuleItem'
import { queryCalRuleDetailApi, updateCommonRuleApi } from '_/api/costCharging'
import { isSuccess } from '_/utils/code'
import { CalRuleDetailRes, UpdateCommonRuleReq, UpdateCommonRuleRes } from '_/api/types/costCharging'
import { debounce, isNumber } from 'radash'
import { LayoutConstant } from '_/assets/constant/layout'
import { _ } from 'numeral'
/** 校验函数的类型 */
// 需要知道第几行 第几列 数据 值类型 对象字段
type Validator = (index: number, column: string, data: any, valueType: 'primitive' | 'object', objectField?: string) => string

// 由于不知道后端会不会增加全部的枚举，不知道全部的枚举值,可能还会有许多其他的卡类型
const CardTypes = [{
  key: 'DEBIT',
  value: '借记卡'
}, {
  key: 'CREDIT',
  value: '贷记卡'
}] as EnumBase[]

// 定义需要费用类型的清算机构
const ClearingOrgMap = {
  UNION_PAY: 'UNION_PAY',
  AMEX: 'AMEX'
}
const FeeTypeClearingOrg = Object.values(ClearingOrgMap) as string[]

// // 定义需要费用类型的渠道标识
// const TagsMap = {
//   INSTITUTION: 'UNION_ORG', // 银联-机构号
// }
// // 三段式费用类型
// const ThreeFeeTypeTags = Object.values(TagsMap) as string[]

export default defineComponent({
  name: 'CommonRuleConfig',
  components: {
    RuleItem
  },
  data() {
    return {
      pageCode: 'commonRuleConfig',
      schema: {} as any,
      getLabelFromEnum,
      getUid,
      FeeTypeClearingOrg, // 需要费用类型的清算机构
      // currentFeeTypeTags: 1, // 当前费用模式标识
      currentFeeTypes: [] as EnumBase[], // 当前费用类型
      // 显隐控制
      commonModalVisible: false, // 通用详情配置弹窗
      // 加载中
      submiting: false, // 提交状态
      // 表单布局
      layout: {
        layout: 'horizontal',
        labelCol: { span: 4 },
        wrapperCol: { span: 20 },
      },

      // 原始数据
      calFeeRuleDetailOrg: {
      } as CalFeeRuleDetail,
      // 当前操作id
      currentRouteId: undefined as number | undefined,
      // 通用配置表单数据
      form: {
        clearingOrgCode: '',
        settlementType: '',
        calculateType: '',
        ruleName: '',
        // tags: '' // 渠道标识
      } as Omit<CalFeeRuleDetail, 'calRules'>,
      // 通用手续费展示数据
      tableData: [] as CalFeeRuleDisplay[],
      // 通用手续费校验规则
      tableEditValRules: {} as Record<string, { required?: boolean, validator: boolean | Validator, message?: string, fieldType: 'primitive' | 'object' }>,

      // eles layout
      elesLayout: LayoutConstant,
      // 如何渲染渠道手续费配置
      // 清算机构枚举
      clearingOrgEnum: [] as EnumBase[],
      // 费用类型枚举
      feeTypeEnum: [] as EnumBase[],
      // 结算方式
      settlementTypeEnum: [] as EnumBase[],
      // 计费类型
      calculateTypeEnum: [] as EnumBase[],
      // 交易类型
      tradeTypeEnum: [] as EnumBase[],
      // 卡类型
      cardTypeEnum: [] as EnumBase[],
      // YN枚举
      yNEnum: [] as EnumBase[],
      // 计算类型
      calConfigRuleEnum: [] as EnumBase[],
      // 渠道标识
      tagsEnum: [] as EnumBase[]
    }
  },
  computed: {
    /** 低代码关联 */
    commonRuleConfig() {
      return (this as any).__yeepay_lowcode_commonRuleConfig__.value
    },
    // 列配置
    feeColumns() {
      const _columns = []
      // 卡类型
      _columns.push({
        title: '卡类型',
        dataIndex: 'cardType',
        width: 80,
        align: 'center',
        key: 'cardType',
        scopedSlots: { customRender: 'cardType' }
      })
      // 如果渠道标识是三段式费用类型
      console.log('tags', this.feeTypeNum, this.feeTypeNum > 1)

      if (this.feeTypeNum > 1) {
        _columns.push({
          title: '费用类型',
          dataIndex: 'feeType',
          width: 90,
          align: 'center',
          key: 'feeType',
          scopedSlots: { customRender: 'feeType' }
        })
      }
      // 支付
      _columns.push({
        title: '支付',
        dataIndex: 'PAY',
        key: 'PAY',
        width: 450,
        align: 'center',
        scopedSlots: { customRender: 'PAY' }
      })
      // 退款
      _columns.push({
        title: '退款',
        dataIndex: 'REFUND',
        key: 'REFUND',
        width: 450,
        align: 'center',
        scopedSlots: { customRender: 'REFUND' }
      })
      return _columns
    },
    tableObjectElementMap() {
      return {
        // 计算类型
        calConfigElement: {
          dataIndex: 'calConfigRule',
          title: '计算类型',
          type: 'select',
          span: 12,
          layout: this.elesLayout[12],
          options: this.calConfigRuleEnum.map((item: EnumBase) => ({ label: item.value, value: item.key }))
        },
        // 配置值
        valueElement: {
          dataIndex: 'value',
          title: '配置值',
          type: 'number',
          precision: 4,
          min: 0,
          step: 0.0001,
          span: 12,
          layout: this.elesLayout[12]
        },
        // 保底金额
        minValueElement: {
          dataIndex: 'minValue',
          title: '保底金额',
          type: 'number',
          min: 0,
          max: 9999999.9999,
          precision: 4,
          step: 0.0001,
          span: 12,
          layout: this.elesLayout[12]
        },
        // 封顶金额
        maxValueElement: {
          dataIndex: 'maxValue',
          title: '封顶金额',
          type: 'number',
          min: 0.0001,
          max: 9999999.9999,
          span: 12,
          precision: 4,
          step: 0.0001,
          layout: this.elesLayout[12]
        },
        // 按比例退款
        refundByProportionElement: {
          dataIndex: 'refundByProportion',
          title: '按比例',
          type: 'radio',
          span: 24,
          options: this.yNEnum.map((item: EnumBase) => ({ label: item.value, value: item.key })),
          layout: this.elesLayout[24]
        },
      }
    },
    feeTypeNum() {
      const _feeTypes = this.currentFeeTypes || []
      return _feeTypes.length > 1 ? _feeTypes.length : 1
    }
  },
  watch: {
    'form.calculateType': {
      handler(newVal: string, oldVal: string) {
        if (newVal === oldVal || !newVal) {
          return
        }
        if (!this.form.clearingOrgCode) {
          this.$message.error('清算机构和计算类型不能为空')
          return
        }
        // 他俩都有值触发更新
        if (this.form.clearingOrgCode && newVal) {
          const _calRules = JSON.parse(JSON.stringify(this.calFeeRuleDetailOrg.calRules || []))
          this.initData(this.form.clearingOrgCode, newVal, _calRules, this.feeTypeNum)
        }
      }
    }
  },
  methods: {
    // 打开弹窗
    openDialog(routeId: number) {
      if (!routeId) {
        this.$message.error('规则路由id获取异常')
        return
      }
      this.currentRouteId = routeId
      // 获取枚举数据
      this.commonRuleConfig.runJs(({ queries }: { queries: any }) => {
        this.clearingOrgEnum = queries.enums.data.clearingOrgEnum
        this.feeTypeEnum = queries.enums.data.feeTypeEnum
        this.settlementTypeEnum = queries.enums.data.settlementTypeEnum
        this.tradeTypeEnum = queries.enums.data.tradeTypeEnum
        this.cardTypeEnum = queries.enums.data.cardTypeEnum
        this.calculateTypeEnum = queries.enums.data.calculateTypeEnum
        this.yNEnum = queries.enums.data.yNEnum
        this.calConfigRuleEnum = queries.enums.data.calConfigRuleEnum
        this.tagsEnum = queries.enums.data.payInterfaceTagEnum
      })
      // 获取规则详情
      queryCalRuleDetailApi({ routeId }).then((res: CalRuleDetailRes) => {
        const { code, data } = res
        if (isSuccess(code)) {
          this.calFeeRuleDetailOrg = JSON.parse(JSON.stringify(data))
          // 将数据转换为表单数据
          const { clearingOrgCode, settlementType, calculateType, ruleName, feeTypes } = data
          this.form.clearingOrgCode = clearingOrgCode || ''
          this.form.settlementType = settlementType || ''
          this.form.calculateType = calculateType || ''
          this.form.ruleName = ruleName || ''
          // this.currentFeeTypeTags = feeTypeNum || 1
          this.currentFeeTypes = (feeTypes || []).map((item: string) => {
            return {
              key: item,
              value: getLabelFromEnum(item, this.feeTypeEnum, ['value', 'key'])
            } as EnumBase
          })
          // 这里理论上会触发数据初始化
          this.commonModalVisible = true
        } else {
          this.$message.error('获取规则详情失败')
        }
      })
    },
    // 清算机构 + 计费类型 + rules + 渠道标识
    initData(clearingOrgCode: string, calculateType: string = 'NO_STEP', rules: CalFeeRule[], feeTypeNum: number = 1) {
      // --------------------------初始化校验规则--------------------------------
      this.tableEditValRules = {
        calConfigRule: {
          required: true,
          validator: (index: number, column: string, data: any) => {
            const _data = this.tableData[index][column]
            if (column === 'REFUND' && _data.refundByProportion !== 'N') {
              return ''
            }
            if (Array.isArray(data) && data.length === 0) {
              return '必填'
            }
            if (data === '' || data === null || data === undefined) {
              return '必填'
            }
            return ''
          },
          fieldType: 'object',
          message: '请输入',
        },
        value: {
          required: true,
          validator: (index: number, column: string, data: any) => {
            const _data = this.tableData[index][column]
            if (column === 'REFUND' && _data.refundByProportion !== 'N') {
              return ''
            }
            if (Array.isArray(data) && data.length === 0) {
              return '必填'
            }
            if (!isNumber(data)) {
              return '必填'
            }
            return ''
          },
          fieldType: 'object',
          message: '请输入',
        },
        minValue: {
          validator: false,
          fieldType: 'object',
          message: '请输入',
        },
        maxValue: {
          validator: false,
          fieldType: 'object',
          message: '请输入',
        },
        refundByProportion: {
          required: true,
          validator: (index: number, column: string, data: any) => {
            const _data = this.tableData[index][column]
            // 支付的不校验
            if (column === 'PAY') {
              return ''
            }
            if (Array.isArray(data) && data.length === 0) {
              return '必填'
            }
            if (data === '' || data === null || data === undefined) {
              return '必填'
            }
            return ''
          },
          fieldType: 'object',
          message: '请选择',
        },
        // 原则，只校验关联的逻辑
        PAY: {
          validator: (index: number, column: string, data: any) => {
            if (+data.maxValue === 0) {
              return ''
            }
            if (+data.minValue > +data.maxValue) {
              return '保底金额不能大于封顶金额'
            }
            return ''
          },
          fieldType: 'primitive',
          message: '请选择',
        },
        REFUND: {
          validator: (index: number, column: string, data: any) => {
            if (+data.maxValue === 0) {
              return ''
            }
            if (+data.minValue > +data.maxValue) {
              return '保底金额不能大于封顶金额'
            }
            return ''
          },
          message: '请选择',
          fieldType: 'primitive',
        }
      }
      // --------------------------初始化规则--------------------------------
      const allRules = Array.isArray(rules) ? rules : []
      // 每种计费类型的展示内容不一样

      // 机构有区别this.FeeTypeClearingOrg 和 ['NUCC']

      // 展示层的数据按照卡类型+费用类型维度，将支付和退款组合到一起
      const _tableData = [] as CalFeeRuleDisplay[] // 每条数据的信息为卡类型、费用类型、支付、退款
      // 由于是通用配置，需要增加卡类型的维度
      CardTypes.forEach((cardTypeItem: EnumBase) => {
        // 只获取目标卡类型的数据
        const pickRules = allRules.filter((rule: CalFeeRule) => {
          return rule.cardType === cardTypeItem.key
        })
        // 需要考虑费用类型
        // 根据渠道标识判断 费用类型为三段式
        // if (this.ThreeFeeTypeTags.includes(tags)) {
        if (feeTypeNum > 1) {
          // 银联和联调的没有费用类型
          // 过滤掉费用类型为ALL的数据
          const _feeTypeEnum = this.currentFeeTypes.filter((feeTypeItem: EnumBase) => {
            return feeTypeItem.key !== 'ALL'
          })
          // 过滤
          _feeTypeEnum.forEach((feeTypeItem: EnumBase) => {
            // 找一条支付
            const findPayRule = pickRules.find((rule: CalFeeRule) => rule.tradeType === 'PAY' && rule.feeType === feeTypeItem.key)
            // 找一条退款
            const findRefundRule = pickRules.find((rule: CalFeeRule) => rule.tradeType === 'REFUND' && rule.feeType === feeTypeItem.key)
            // 确定支付退款
            const payRule = findPayRule || this.getDefaultCalFeeRuleByCalFeeType('PAY', 'NO_STEP', feeTypeItem.key)
            const refundRule = findRefundRule || this.getDefaultCalFeeRuleByCalFeeType('REFUND', 'NO_STEP', feeTypeItem.key)
            _tableData.push({
              id: this.getUid(),
              cardType: cardTypeItem.key,
              cardType__desc: getLabelFromEnum(cardTypeItem.key, this.cardTypeEnum, ['value', 'key']),
              feeType: payRule.feeType,
              feeType__desc: getLabelFromEnum(payRule.feeType, this.feeTypeEnum, ['value', 'key']),
              PAY: payRule,
              PAY__object: {
                elements: [
                  this.tableObjectElementMap.calConfigElement,
                  {
                    ...this.tableObjectElementMap.valueElement,
                    ...this.getCalConfigRuleMaxAndTitle(payRule.calConfigRule),
                  },
                  this.tableObjectElementMap.minValueElement,
                  this.tableObjectElementMap.maxValueElement,
                ]
              },
              REFUND: refundRule,
              REFUND__object: {
                elements: [
                  this.tableObjectElementMap.refundByProportionElement,
                  ...(refundRule.refundByProportion === 'N' ? [
                    this.tableObjectElementMap.calConfigElement,
                    {
                      ...this.tableObjectElementMap.valueElement,
                      ...this.getCalConfigRuleMaxAndTitle(refundRule.calConfigRule),
                    },
                  ] : []),
                  this.tableObjectElementMap.minValueElement,
                  this.tableObjectElementMap.maxValueElement,
                ]
              }
            })
          })
        } else {
          // 非三段式费用类型，一段式
          // 找一条支付
          const findPayRule = pickRules.find((rule: CalFeeRule) => rule.tradeType === 'PAY')
          // 找一条退款
          const findRefundRule = pickRules.find((rule: CalFeeRule) => rule.tradeType === 'REFUND')
          // 确定支付退款
          const payRule = findPayRule || this.getDefaultCalFeeRuleByCalFeeType('PAY', 'NO_STEP', '')
          const refundRule = findRefundRule || this.getDefaultCalFeeRuleByCalFeeType('REFUND', 'NO_STEP', '')
          _tableData.push({
            id: this.getUid(),
            cardType: cardTypeItem.key,
            cardType__desc: getLabelFromEnum(cardTypeItem.key, this.cardTypeEnum, ['value', 'key']),
            PAY: payRule,
            PAY__object: {
              elements: [
                this.tableObjectElementMap.calConfigElement,
                {
                  ...this.tableObjectElementMap.valueElement,
                  ...this.getCalConfigRuleMaxAndTitle(payRule.calConfigRule),
                },
                this.tableObjectElementMap.minValueElement,
                this.tableObjectElementMap.maxValueElement,
              ]
            },
            REFUND: refundRule,
            REFUND__object: {
              elements: [
                this.tableObjectElementMap.refundByProportionElement,
                ...(refundRule.refundByProportion === 'N' ? [
                  this.tableObjectElementMap.calConfigElement,
                  {
                    ...this.tableObjectElementMap.valueElement,
                    ...this.getCalConfigRuleMaxAndTitle(refundRule.calConfigRule),
                  },
                ] : []),
                this.tableObjectElementMap.minValueElement,
                this.tableObjectElementMap.maxValueElement,
              ]
            }
          })
        }
      })
      this.tableData = _tableData
    },
    // 根据计算方式返回配置值的max和title
    getCalConfigRuleMaxAndTitle(calConfigRule: string = 'FIXED') {
      let max = 100; let title = '配置值'
      if (calConfigRule === 'FIXED') {
        max = 99999.9999
        title = '配置值(元)'
      } else if (calConfigRule === 'RATE') {
        max = 100
        title = '配置值(%)'
      }
      return { max, title }
    },
    onSubmit() {
      // 验证
      this.$refs.formRef.validate((valid: boolean) => {
        if (valid) {
          const hasErr = this.onValidateAll()
          if (hasErr) {
          } else {
            // 设置提交状态
            this.submiting = true
            // this.$message.success('所有校验通过')
            // 准备待提交数据
            const _calculItems: CalFeeRule[] = []
            this.tableData.forEach((row: CalFeeRuleDisplay) => {
              const _pay = row.PAY
              const _refund = row.REFUND
              _calculItems.push({
                id: _pay.id,
                calFeeType: _pay.calFeeType,
                calRuleId: _pay.calRuleId,
                refundByProportion: _pay.refundByProportion,
                calConfigRule: _pay.calConfigRule,
                value: _pay.value,
                maxValue: _pay.maxValue,
                minValue: _pay.minValue,
                tradeType: _pay.tradeType,
                feeType: _pay.feeType,
                cardType: row.cardType || ''
              })
              _calculItems.push({
                id: _refund.id,
                calFeeType: _refund.calFeeType,
                calRuleId: _refund.calRuleId,
                refundByProportion: _refund.refundByProportion,
                calConfigRule: _refund.refundByProportion === 'N' ? _refund.calConfigRule : '',
                value: _refund.refundByProportion === 'N' ? _refund.value : '',
                maxValue: _refund.maxValue,
                minValue: _refund.minValue,
                tradeType: _refund.tradeType,
                feeType: _refund.feeType,
                cardType: row.cardType || ''
              })
            })
            const reqData = {} as UpdateCommonRuleReq
            reqData.ruleRouteId = this.currentRouteId as number
            reqData.calculateType = this.form.calculateType
            reqData.ruleRouteName = this.form.ruleName
            // 需要按照计费类型进行逻辑取值吗？
            reqData.calRuleDetailVos = _calculItems
            // 调用接口
            updateCommonRuleApi(reqData).then((res: UpdateCommonRuleRes) => {
              const { code, message } = res
              if (isSuccess(code)) {
                this.$message.success('修改成功')
                // 关闭弹窗
                this.onClose()
                // 触发低代码页面
                this.commonRuleConfig.runQuery('pageList')
              } else {
                this.$message.error(message)
              }
            }).finally(() => {
              this.submiting = false
            })
          }
        } else {
          return false
        }
      })
    },
    // 关闭弹窗
    onClose() {
      // 数据清理
      // 清理form
      this.form = {
        clearingOrgCode: '',
        settlementType: '',
        calculateType: '',
        ruleName: '',
        // tags: ''
      }
      // 清理原始值
      this.calFeeRuleDetailOrg = {} as CalFeeRuleDetail
      // 清理tableData
      this.tableData = []
      // 清理校验规则
      this.tableEditValRules = {}
      // 关闭弹窗
      this.commonModalVisible = false
    },
    /**
     * 根据计费类型和交易类型获取默认计费规则 现在只考虑了无阶梯
     * @param tradeType 交易类型
     * @param calFeeType 计费类型
     * @param feeType 费用类型
     */
    getDefaultCalFeeRuleByCalFeeType(tradeType: string, calFeeType: string, feeType: string): CalFeeRule {
      const calFeeRule = {} as CalFeeRule
      const _calFeeSence = ['NO_STEP', 'SINGLE_STEP', 'CUMULATIVE_STEP']
      if (tradeType === 'PAY') {
        if (_calFeeSence.includes(calFeeType)) {
          calFeeRule.calConfigRule = 'FIXED'
          // 保底
          calFeeRule.minValue = ''
          // 封顶
          calFeeRule.maxValue = ''
        }
      } else if (tradeType === 'REFUND') {
        // 所有的退款都有的
        calFeeRule.refundByProportion = ''
        if (_calFeeSence.includes(calFeeType)) {
          calFeeRule.calConfigRule = 'FIXED'
          // 保底
          calFeeRule.minValue = ''
          // 封顶
          calFeeRule.maxValue = ''
        }
      }
      // 定义交易类型
      calFeeRule.tradeType = tradeType
      // 定义计费类型
      calFeeRule.calFeeType = calFeeType
      // 定义费用类型
      calFeeRule.feeType = feeType
      return calFeeRule
    },
    /**
     * @description 单元格变更
     * @param { number } index 第几行
     * @param { string } column 第几列
     * @param data 数据
     * @param { boolean } isVal 是否校验
     * @param { CellOptions } options 配置选项
     */
    onCellChange(index: number, column: string, data: any, isVal: boolean = true, options: CellOptions = { valueType: 'primitive' }) {
      if (options.valueType === 'primitive') {
        this.tableData[index][column] = JSON.parse(JSON.stringify(data))
        // 执行副作用
        if (options.callback) {
          options.callback(index, column, data, 'primitive')
        }
      } else if (options.valueType === 'object') {
        this.tableData[index][column][options.objectConfig ? options.objectConfig.field : ''] = JSON.parse(JSON.stringify(data))
        // 执行副作用
        if (options.callback) {
          options.callback(index, column, data, 'object', options.objectConfig && options.objectConfig.field)
        }
        // ----副作用----
        // 如果退款按比例退款，需要将计算类型字段相关字段移除
        const _field = options.objectConfig && options.objectConfig.field
        if (column === 'REFUND' && _field === 'refundByProportion') {
          // 如果是Y
          if (data === 'Y') {
            // 需要将计算类型字段相关字段移除
            // 清空退款值
            this.tableData[index].REFUND.calConfigRule = ''
            this.tableData[index].REFUND.value = ''
            // 更新elemenets
            // 拿到REFUND__object 下的elements
            const elements = this.tableData[index].REFUND__object.elements
            // 找到calConfigRule字段，并移除
            const calConfigRuleIndex = elements.findIndex((ele: any) => ele.dataIndex === 'calConfigRule')
            calConfigRuleIndex > 0 && elements.splice(calConfigRuleIndex, 1)
            // 找到value字段，并移除
            const valueIndex = elements.findIndex((ele: any) => ele.dataIndex === 'value')
            valueIndex > 0 && elements.splice(valueIndex, 1)
          } else if (data === 'N') {
            // 需要将计算类型字段相关加入
            // 拿到PAY_object 下的elements
            const elements = this.tableData[index].REFUND__object.elements
            // 拿到退款值
            const _refund = this.tableData[index].REFUND

            // 重置退款值
            this.tableData[index].REFUND.calConfigRule = 'FIXED'
            this.tableData[index].REFUND.value = ''

            // 更新elements
            // 找到refundByProportion字段
            const refundByProportionIndex = elements.findIndex((ele: any) => ele.dataIndex === 'refundByProportion')
            // 找到calConfigRule字段，并加入
            const calConfigRuleIndex = elements.findIndex((ele: any) => ele.dataIndex === 'calConfigRule')
            !(calConfigRuleIndex > 0) && elements.splice(refundByProportionIndex + 1, 0, this.tableObjectElementMap.calConfigElement)
            // 找到value字段，并加入
            const valueIndex = elements.findIndex((ele: any) => ele.dataIndex === 'value')
            !(valueIndex > 0) && elements.splice(refundByProportionIndex + 2, 0, {
              ...this.tableObjectElementMap.valueElement,
              ...this.getCalConfigRuleMaxAndTitle(_refund.calConfigRule),
            })
          }
        } else if (['PAY', 'REFUND'].includes(column) && _field === 'calConfigRule') {
          // 如果是配置规则的变更，需要动态改value的配置
          // TODO: 需要动态改value的配置
          const _calConfigRuleValue = this.tableData[index][column][_field]
          const elements = this.tableData[index][column + '__object'].elements
          if (_calConfigRuleValue === 'FIXED') {
            // 如果是固定金额，则需要将value的值乘以100
            // 拿到elements
            elements.find((ele: any) => ele.dataIndex === 'value').max = 99999.9999
          } else if (_calConfigRuleValue === 'RATE') {
            // 如果是费率，则需要将value的值除以100
            elements.find((ele: any) => ele.dataIndex === 'value').max = 100
          }
          // 设置value的值为''
          // 这里直接改值不会触发校验
          this.tableData[index][column].value = ''
        }
      }
      // 找到每一列的规则
      // 根据值类型，找到对应的校验规则
      let validator: boolean | Validator = false
      if (options.valueType === 'primitive') {
        validator = this.tableEditValRules[column] ? this.tableEditValRules[column].validator : false
        if (validator !== false) {
          this.onCellValidate(index, column, data, validator, options)
        }
      } else if (options.valueType === 'object') {
        const _field = options.objectConfig ? options.objectConfig.field : ''
        validator = this.tableEditValRules[_field] ? this.tableEditValRules[_field].validator : false
        // 如果当前字段需要校验，则校验，内部会判断对象整体需不需要
        if (validator !== false) {
          this.onCellValidate(index, column, data, validator, options)
        } else {
          // 如果当前字段不需要校验，则判断对象整体需不需要
          if (this.tableEditValRules[column] && this.tableEditValRules[column].validator !== false) {
            this.onCellValidate(index, column, this.tableData[index][column], this.tableEditValRules[column].validator, options = { valueType: 'primitive' })
          }
        }
      }
    },

    /**
     * @description 规则明细单元格校验
     * @param { number } index 第几行
     * @param { string } column 第几列
     * @param { any } data 数据
     * @param { boolean | Validator } validator 校验规则
     * @param { CellOptions } options 配置选项
     */
    onCellValidate(index: number, column: string, data: any, validator: boolean | Validator, options: CellOptions = { valueType: 'primitive' }): boolean {
      let hasErr = false
      let _validator = validator
      if (options.valueType === 'primitive') {
        // 如果是boolean类型，定义validator为通用必填校验规则
        if (typeof validator === 'boolean') {
          if (validator) {
            _validator = (value: any) => {
            // 校验必填
            // 校验通过返回空字符串，校验不通过返回错误信息
              if (Array.isArray(value) && value.length === 0) {
                return this.tableEditValRule[column].message || '必填'
              }
              if (value === '' || value === null || value === undefined) {
                return this.tableEditValRule[column].message || '必填'
              }
              return ''
            }
          } else {
            // 如果设置为不校验，则清空错误信息
            this.$set(this.tableData[index], column + '__err_msg', '')
          }
        }

        // 如果是函数类型，直接调用函数。归一化，到这里一定是函数
        if (typeof _validator === 'function') {
          const result = _validator(index, column, data, 'primitive')
          if (result) {
            // 校验不通过
            hasErr = true
            // 使用$set，否则无法触发视图更新
            this.$set(this.tableData[index], column + '__err_msg', result)
          } else {
            // 校验通过
            hasErr = false
            this.$set(this.tableData[index], column + '__err_msg', '')
          }
        }
      } else if (options.valueType === 'object') { // 如果是object类型，则校验对象中的每个字段
        if (typeof validator === 'boolean') {
          if (validator) {
            _validator = (index: number, column: string, data: any, valueType: string, objectField?: string) => {
            // 校验必填
            // 校验通过返回空字符串，校验不通过返回错误信息
              if (Array.isArray(data) && data.length === 0) {
                return '必填'
              }
              if (data === '' || data === null || data === undefined) {
                return '必填'
              }
              return ''
            }
          } else {
          // 如果设置为不校验，则清空错误信息
            options.objectConfig && this.$set(this.tableData[index][column], options.objectConfig.field + '__err_msg', '')
          }
        }

        // 如果是函数类型，直接调用函数。归一化，到这里一定是函数
        if (typeof _validator === 'function') {
          const result = _validator(index, column, data, 'object', options.objectConfig && options.objectConfig.field)
          if (result) {
          // 校验不通过
            hasErr = true
            const _data = JSON.parse(JSON.stringify(this.tableData[index][column]))
            options.objectConfig && this.$set(_data, options.objectConfig.field + '__err_msg', result)
            if (options.objectConfig) {
              this.tableData[index][column] = {
                ..._data,
                [options.objectConfig.field + '__err_msg']: result
              }
            }
          } else {
          // 校验通过
            hasErr = false
            const _data = JSON.parse(JSON.stringify(this.tableData[index][column]))
            options.objectConfig && this.$set(_data, options.objectConfig.field + '__err_msg', '')
            if (options.objectConfig) {
              this.tableData[index][column] = {
                ..._data,
                [options.objectConfig.field + '__err_msg']: ''
              }
            }
          }
        }
        // 如果这个对象需要整体校验
        if (this.tableEditValRules[column]) {
          let _hasErr = false
          _hasErr = this.onCellValidate(index, column, this.tableData[index][column], this.tableEditValRules[column].validator, options = { valueType: 'primitive' })
          hasErr = hasErr || _hasErr
        }
      }
      return hasErr
    },
    // 渠道手续费整体校验
    onValidateAll(): boolean {
      let hasErr = false
      // 遍历tableData，对每一行进行校验
      this.tableData.forEach((row: any, index: number) => {
        Object.entries(this.tableEditValRules).forEach(([key, rule]: [string, any]) => {
          if (rule.fieldType === 'object') {
            const options: CellOptions = { valueType: 'object', objectConfig: { field: key } }
            hasErr = this.onCellValidate(index, 'PAY', row['PAY'][key], rule.validator, options) || hasErr
            hasErr = this.onCellValidate(index, 'REFUND', row['REFUND'][key], rule.validator, options) || hasErr
          } else {
            hasErr = this.onCellValidate(index, key, row[key], rule.validator, { valueType: 'primitive' }) || hasErr
          }
        })
      })
      return hasErr
    }
  },
  async mounted() {
    this.schema = await fetchSchema(this.pageCode)
    // 将原始 onSubmit 方法包装成防抖函数
    const originalSubmit = this.onSubmit
    this.onSubmit = debounce({ delay: 200 }, function() {
      // 这行不检查
      originalSubmit.call(this)
    })
  }
})
</script>
<template>
  <div class="p-5">
    <low-code-renderer v-if="schema.components" :id="pageCode" :schema="schema" @openDialog="openDialog"/>

    <!-- 通用详情配置弹窗 -->

    <a-drawer
      title="编辑通用配置"
      width="1200"
      :closable="false"
      :visible="commonModalVisible"
      class="common-rule-drawer"
      @close="onClose"
    >
      <a-spin :spinning="submiting">
        <a-form-model ref="formRef" class="common-rule-form" :model="form" v-bind="layout">
          <a-form-model-item label="清算机构">
            {{ getLabelFromEnum(form.clearingOrgCode, clearingOrgEnum, ['value', 'key']) || '--' }}
          </a-form-model-item>
          <a-form-model-item label="结算方式">
            {{ getLabelFromEnum(form.settlementType, settlementTypeEnum, ['value', 'key']) || '--' }}
          </a-form-model-item>
          <a-form-model-item label="计算规则名称" required>
            {{ form.ruleName || '--' }}
          </a-form-model-item>
          <a-form-model-item label="计费类型" required>
            <a-radio-group v-model="form.calculateType" :disabled="!!calFeeRuleDetailOrg.calculateType">
              <a-radio
                v-for="item in calculateTypeEnum"
                :key="item.key"
                :value="item.key"
                :disabled="item.key !== 'NO_STEP'"
              >
                {{ item.value }}
              </a-radio>
            </a-radio-group>
          </a-form-model-item>

        </a-form-model>
        <a-form-model-item label="渠道手续费配置" :wrapperCol="{ span: 24 }">
          <rule-item
            :columns="feeColumns"
            :dataSource="tableData"
            :pagination="false"
            :rules="tableEditValRules"
            :rowKey="'id'"
            bordered
            @cellChange="onCellChange"
          />
        </a-form-model-item>
        <div
          :style="{
            position: 'absolute',
            right: 0,
            bottom: 0,
            width: '100%',
            borderTop: '1px solid #e9e9e9',
            padding: '10px 16px',
            background: '#fff',
            textAlign: 'right',
            zIndex: 1,
          }"
        >
          <a-button :style="{ marginRight: '8px' }" @click="onClose">
            取消
          </a-button>
          <a-button
            type="primary"
            @click="onSubmit"
          >
            确认
          </a-button>
        </div>
      </a-spin>
    </a-drawer>
  </div>
</template>
<style lang="less" scoped>
.common-rule-form {

  ::v-deep .ant-form-item {
    margin-bottom: 0;
  }
  ::v-deep .ant-table-body {
    margin: 0 !important;
  }
  ::v-deep .ant-table-tbody > tr:hover:not(.ant-table-expanded-row):not(.ant-table-row-selected) > td {
    background: transparent;
  }
}
.common-rule-drawer{
  ::v-deep .ant-drawer-body {
    padding-bottom: 54px !important;
  }
  ::v-deep .ant-spin-nested-loading {
    position: inherit !important;
  }
  ::v-deep .ant-spin-container {
    position: inherit !important;
  }
}
</style>
