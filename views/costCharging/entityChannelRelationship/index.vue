<script lang="ts">
import { defineComponent } from '@vue/composition-api'
import { fetchSchema } from '@/utils/lowcode'
export default defineComponent({
  name: 'EntityChannelRelationship',
  data() {
    return {
      pageCode: 'entityChannelRelationship',
      schema: {} as any
    }
  },
  async mounted() {
    this.schema = await fetchSchema(this.pageCode)
  }
})
</script>
<template>
  <div class="p-5">
    <low-code-renderer v-if="schema.components" :schema="schema" />
  </div>
</template>

<style lang="less" scoped>

</style>
