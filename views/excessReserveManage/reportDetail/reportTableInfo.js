import { REPORT_1_1_COLUMNS, REPORT_1_1_FIELDS } from './defines/REPORT_1_1'
import { REPORT_1_1_A_COLUMNS, REPORT_1_1_A_FIELDS } from './defines/REPORT_1_1_A'
import { REPORT_1_2_COLUMNS, REPORT_1_2_FIELDS } from './defines/REPORT_1_2'
import { REPORT_1_3_COLUMNS, REPORT_1_3_FIELDS } from './defines/REPORT_1_3'
import { REPORT_1_4_COLUMNS, REPORT_1_4_FIELDS } from './defines/REPORT_1_4'
import { REPORT_1_5_COLUMNS, REPORT_1_5_FIELDS } from './defines/REPORT_1_5'

const NuccAndAmexReportInfo = [
  // REPORT_1_1
  {
    title: '表1.1：入金表',
    tableKey: 'REPORT_1_1',
    columns: REPORT_1_1_COLUMNS,
    fields: REPORT_1_1_FIELDS
  },
  // REPORT_1_2
  {
    title: '表1.2：出金表',
    tableKey: 'REPORT_1_2',
    columns: REPORT_1_2_COLUMNS,
    fields: REPORT_1_2_FIELDS
  },
  // REPORT_1_5
  {
    title: '表1.5：特殊业务表',
    tableKey: 'REPORT_1_5',
    columns: REPORT_1_5_COLUMNS,
    fields: REPORT_1_5_FIELDS
  },
]

const UnionpayReportInfo = [
  // REPORT_1_1
  {
    title: '表1.1：入金表',
    tableKey: 'REPORT_1_1',
    columns: REPORT_1_1_COLUMNS,
    fields: REPORT_1_1_FIELDS
  },
  // REPORT_1_2
  {
    title: '表1.2：出金表',
    tableKey: 'REPORT_1_2',
    columns: REPORT_1_2_COLUMNS,
    fields: REPORT_1_2_FIELDS
  },
  // REPORT_1_5
  {
    title: '表1.5：特殊业务表',
    tableKey: 'REPORT_1_5',
    columns: REPORT_1_5_COLUMNS,
    fields: REPORT_1_5_FIELDS
  },
  // REPORT_1_1_A
  {
    title: '表1.1A：直接收款表',
    tableKey: 'REPORT_1_1_A',
    columns: REPORT_1_1_A_COLUMNS,
    fields: REPORT_1_1_A_FIELDS
  },
  // REPORT_1_3
  {
    title: '表1.3：转账表',
    tableKey: 'REPORT_1_3',
    columns: REPORT_1_3_COLUMNS,
    fields: REPORT_1_3_FIELDS
  },
  // REPORT_1_4
  {
    title: '表1.4：客户账表',
    tableKey: 'REPORT_1_4',
    columns: REPORT_1_4_COLUMNS,
    fields: REPORT_1_4_FIELDS
  },
]

const ReportTableInfo = {
  NuccAndAmexReportInfo,
  UnionpayReportInfo
}

export default ReportTableInfo
