export const CalcProcessColumns = [
  {
    title: '业务类型',
    dataIndex: 'trxType',
    key: 'trxType',
    width: 140,
    ellipsis: true,
  },
  {
    title: '符号',
    dataIndex: 'symbol',
    key: 'symbol',
    width: 50
  },
  {
    title: '金额(元)',
    dataIndex: 'payAmount',
    key: 'payAmount',
    width: 140,
    align: 'right',
    scopedSlots: { customRender: 'payAmount' },
  },
]

export const AdjustRecordColumns = [
  {
    title: '业务类型',
    dataIndex: 'businessType',
    key: 'businessType',
    width: 100
  },
  {
    title: '符号',
    dataIndex: 'symbol',
    key: 'symbol',
    width: 60
  },
  {
    title: '金额(元)',
    dataIndex: 'amount',
    key: 'amount',
    width: 180,
    align: 'right',
    scopedSlots: { customRender: 'amount' }
  },
  {
    title: '是否计算',
    dataIndex: 'calculated',
    key: 'calculated',
    width: 80,
    scopedSlots: { customRender: 'calculated' }
  }
]
