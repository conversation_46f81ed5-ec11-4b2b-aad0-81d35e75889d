export const REPORT_1_5_COLUMNS = [
  {
    title: '增加备付金存款的业务',
    children: [
      {
        title: '向备付金银行交存现金形式备付金(仅针对持预付卡拍照的支付机构)',
        children: [
          {
            title: 'E01(元)',
            dataIndex: 'E01',
            key: 'E01',
            scopedSlots: { customRender: 'E01' },
          }
        ]
      },
      {
        title: '非备付金集中存管账户计付的利息收入(仅针对持预付卡拍照的支付机构)',
        children: [
          {
            title: 'E02(元)',
            dataIndex: 'E02',
            key: 'E02',
            scopedSlots: { customRender: 'E02' },
          }
        ]
      },
      {
        title: '备付金集中存管账户收到划拨的利息收入(仅单牌照预付卡和多牌照支付机构填报)',
        children: [
          {
            title: 'E03(元)',
            dataIndex: 'E03',
            key: 'E03',
            scopedSlots: { customRender: 'E03' },
          }
        ]
      },
      {
        title: '备付金集中存管账户收到备付金专用存款账户归集的客户备付金(仅单牌照预付卡和多牌照支付机构填报)',
        children: [
          {
            title: 'E04(元)',
            dataIndex: 'E04',
            key: 'E04',
            scopedSlots: { customRender: 'E04' },
          }
        ]
      },
      {
        title: '其他',
        children: [
          {
            title: 'E05(元)',
            dataIndex: 'E05',
            key: 'E05',
            scopedSlots: { customRender: 'E05' },
          }
        ]
      },
    ]
  },
  {
    title: '减少备付金存款的业务',
    children: [
      {
        title: '利息收入划拨进备付金集中存管账户(仅针对持预付卡牌照的支付机构)',
        children: [
          {
            title: 'F01(元)',
            dataIndex: 'F01',
            key: 'F01',
            scopedSlots: { customRender: 'F01' },
          }
        ]
      },
      {
        title: '银行扣取手续费、管理费等费用(仅针对持预付卡牌照的支付机构)',
        children: [
          {
            title: 'F02(元)',
            dataIndex: 'F02',
            key: 'F02',
            scopedSlots: { customRender: 'F02' },
          }
        ]
      },
      {
        title: '结转利息收入(备付金集中存管账户)',
        children: [
          {
            title: 'F03(元)',
            dataIndex: 'F03',
            key: 'F03',
            scopedSlots: { customRender: 'F03' },
          }
        ]
      },
      {
        title: '结转手续费收入(备付金集中存管账户)',
        children: [
          {
            title: 'F04(元)',
            dataIndex: 'F04',
            key: 'F04',
            scopedSlots: { customRender: 'F04' },
          }
        ]
      },
      {
        title: '办理预付卡先行现金赎回业务',
        children: [
          {
            title: 'F05(元)',
            dataIndex: 'F05',
            key: 'F05',
            scopedSlots: { customRender: 'F05' },
          }
        ]
      },
      {
        title: '向备付金集中存管账户划转的客户备付金(备付金专用存款账户)',
        children: [
          {
            title: 'F06(元)',
            dataIndex: 'F06',
            key: 'F06',
            scopedSlots: { customRender: 'F06' },
          }
        ]
      },
      {
        title: '其他',
        children: [
          {
            title: 'F07(元)',
            dataIndex: 'F07',
            key: 'F07',
            scopedSlots: { customRender: 'F07' },
          }
        ]
      },
    ]
  },
]
export const REPORT_1_5_FIELDS = ['E01', 'E02', 'E03', 'E04', 'E05', 'F01', 'F02', 'F03', 'F04', 'F05', 'F06', 'F07']
