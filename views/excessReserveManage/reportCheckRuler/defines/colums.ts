type ValidatorFunction = (data: any) => string
type Column = {
  title?: string
  dataIndex: string
  key: string
  align?: string
  width?: number|string
  /**
   * When using columns, you can use this property to configure the properties that support the slot-scope,
   * such as scopedSlots: { customRender: 'XXX'}
   * @type object
   */
  scopedSlots?: object,
  __validator__?: boolean | ValidatorFunction
}

// 规则配置表头
export const RulerDetailColumn: Column[] = [
  {
    title: '取数逻辑',
    dataIndex: 'rulerItemL',
    width: 370,
    align: 'center',
    key: 'rulerItemL',
    scopedSlots: { customRender: 'rulerItemL' }
  },
  {
    title: '勾稽关系',
    dataIndex: 'checkMethod',
    key: 'checkMethod',
    align: 'center',
    scopedSlots: { customRender: 'checkMethod' }
  },
  {
    title: '取数逻辑',
    dataIndex: 'rulerItemR',
    width: 370,
    align: 'center',
    key: 'rulerItemR',
    scopedSlots: { customRender: 'rulerItemR' }
  }
]

export const RulerItemColumnLeft: Column[] = [
  {
    title: '运算符',
    dataIndex: 'symbol',
    key: 'symbol',
    width: 80,
    scopedSlots: { customRender: 'symbol' },
    __validator__: true,
  },
  {
    title: '表',
    dataIndex: 'reportTable',
    key: 'reportTable',
    width: 115,
    scopedSlots: { customRender: 'reportTable' },
    __validator__: true,
  },
  {
    title: '字段',
    dataIndex: 'reportField',
    key: 'reportField',
    width: 90,
    scopedSlots: { customRender: 'reportField' },
    __validator__: true,
  },
  {
    title: '操作',
    dataIndex: 'action',
    key: 'action',
    width: 50,
    scopedSlots: { customRender: 'action' }
  }
]

// 右侧规则项列配置（添加清算机构列）
export const RulerItemColumnRight: Column[] = [
  {
    title: '清算机构',
    dataIndex: 'clearOrgBank',
    key: 'clearOrgBank',
    width: 80,
    scopedSlots: { customRender: 'clearOrgBank' },
    __validator__: true,
  },
  ...RulerItemColumnLeft
]
