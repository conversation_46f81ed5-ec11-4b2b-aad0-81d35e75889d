<script lang="ts">
/** 库引入 */
import { defineComponent } from '@vue/composition-api'
import { fetchSchema } from '@/utils/lowcode'
import { RulerDetailColumn, RulerItemColumnLeft, RulerItemColumnRight } from './defines/colums'
/** 组件导入 */
import RulerItem from './RulerItem/index.vue'
/** 类型导入 */
import { ReportRulerItem, RulerDetailItem } from '_/types/excessReserveManage/reportCheckRuler'
import { EnumBase } from '_/types/enums/enums'
/** 接口导入 */
import { bankReportEnums } from '_/api/index'
import { operaRulerApi } from '_/api/excessReserve/index'
/** 窗口类型 */
type MODAL_TAG = 'add' | 'edit'
/** 规则信息 */
type RulerInfo = {
  /** 勾稽关系 */
  checkMethod: '=',
  /** 规则左侧信息 */
  rulerItemL: RulerDetailItem[],
  /** 规则右侧信息 */
  rulerItemR: RulerDetailItem[],
}
type OptionBase = {
  /** 状态 */
  value: string,
  /** 状态名称 */
  label: string,
}
/** 核验等式的左右位置 */
type Position = 'L' | 'R'
/** 校验函数的类型 */
type Validator = (data: any) => string

export default defineComponent({
  name: 'ReportCheckRuler',
  components: {
    RulerItem,
  },
  data() {
    return {
      operaRulerApi,
      pageCode: 'reportCheckRuler',
      schema: {} as any,
      drawerModalWidth: 970,
      modalTitle: '',
      modalTag: '',
      layout: {
        labelCol: { span: 4 },
        wrapperCol: { span: 14 },
      },
      // 添加窗口
      addModalVisible: false,
      // 编辑窗口
      editModalVisible: false,
      isSubmitIng: false,
      RulerDetailColumn,
      RulerItemColumnLeft,
      RulerItemColumnRight,
      /** 选中原始数据 */
      originSelectData: {} as ReportRulerItem,
      /** 规则详情 */
      currentRulerInfo: {
        checkMethod: '=',
        rulerItemL: [],
        rulerItemR: [],
      } as RulerInfo,
      rulerForm: {
        clearOrgBank: '',
        status: '',
      },
      rules: {
        clearOrgBank: [{ required: true, message: '请选择清算机构', trigger: 'change' }],
        status: [{ required: true, message: '请选择状态', trigger: 'change' }],
      },

      // 枚举
      clearOrgBankOptions: [] as OptionBase[],
      statusOptions: [] as OptionBase[],
      /** 符号 */
      symbolOptions: [
        { value: '+', label: '+' },
        { value: '-', label: '-' }
      ] as OptionBase[],
      /** 表 */
      reportTableOptions: [] as OptionBase[],
      /** 字段 */
      reportFieldOptions: [] as OptionBase[],
    }
  },
  computed: {
    /** 低代码关联 */
    reportCheckRuler() {
      return (this as any).__yeepay_lowcode_reportCheckRuler__.value
    },
    geneOperateRulerParam() {
      const detailInfoReqVoList: any = []
      const hasId: { id?: number} = {}
      if (this.originSelectData.id) {
        hasId.id = this.originSelectData.id
      }
      this.currentRulerInfo.rulerItemL.forEach((item) => {
        const _: RulerDetailItem = {} as RulerDetailItem
        if (item.id) {
          _.id = item.id
        }
        _.checkPosition = item.checkPosition
        _.symbol = item.symbol
        _.reportTable = item.reportTable
        _.reportField = item.reportField
        detailInfoReqVoList.push(_)
      })
      this.currentRulerInfo.rulerItemR.forEach((item) => {
        const _: RulerDetailItem = {} as RulerDetailItem
        item.id && (_.id = item.id)
        _.checkPosition = item.checkPosition
        _.symbol = item.symbol
        _.reportTable = item.reportTable
        _.reportField = item.reportField
        // 添加清算机构字段到右侧规则项
        if (item.clearOrgBank) {
          _.clearOrgBank = item.clearOrgBank
        }
        detailInfoReqVoList.push(_)
      })
      return {
        ...hasId,
        ...this.rulerForm,
        checkMethod: this.currentRulerInfo.checkMethod,
        detailInfoReqVoList
      }
    }
  },
  methods: {
    /** 初始化弹窗 */
    initForm() {
      this.rulerForm = {
        clearOrgBank: this.originSelectData.clearOrgBank || '',
        status: this.originSelectData.status || '',
      }
      this.currentRulerInfo = {
        checkMethod: this.originSelectData.checkMethod || '=',
        rulerItemL: this.originSelectData.detailVoL ? this.originSelectData.detailVoL.map((_) => {
          return {
            ..._,
            id: _.id,
            __id: _.id + '' + Math.random(),
            symbol: _.symbol,
            symbol__err_msg: '',
            symbol__options: JSON.parse(JSON.stringify(this.symbolOptions)),
            reportTable: _.reportTable,
            reportTable__err_msg: '',
            reportTable__options: JSON.parse(JSON.stringify(this.reportTableOptions)),
            reportField: _.reportField,
            reportField__err_msg: '',
            reportField__options: JSON.parse(JSON.stringify(this.reportFieldOptions)),
          }
        }) : [this.getExpectRulerItem('L')],
        rulerItemR: this.originSelectData.detailVoR ? this.originSelectData.detailVoR.map((_) => {
          return {
            ..._,
            id: _.id,
            __id: _.id + '' + Math.random(),
            symbol: _.symbol,
            symbol__err_msg: '',
            symbol__options: JSON.parse(JSON.stringify(this.symbolOptions)),
            reportTable: _.reportTable,
            reportTable__err_msg: '',
            reportTable__options: JSON.parse(JSON.stringify(this.reportTableOptions)),
            reportField: _.reportField,
            reportField__err_msg: '',
            reportField__options: JSON.parse(JSON.stringify(this.reportFieldOptions)),
            clearOrgBank: _.clearOrgBank || '',
            clearOrgBank__err_msg: '',
            clearOrgBank__options: JSON.parse(JSON.stringify(this.clearOrgBankOptions)),
          }
        }) : [this.getExpectRulerItem('R')],
      }
      // 由于初始化数据时表字段的选项是根据表获取的，所以要请求获取一下
      this.initReportFieldOptionsByTable()
    },
    /** 根据表名更新表字段选项 */
    initReportFieldOptionsByTable() {
      this.currentRulerInfo.rulerItemL.forEach((item, index) => {
        // 如果reportTable有值则更新选项
        item.reportTable && bankReportEnums({
          key: item.reportTable
        }).then(res => {
          const options = res.data[item.reportTable].map((item: EnumBase) => {
            return {
              value: item.key,
              label: item.value,
            }
          }) || []
          // 更新选项
          this.onRulerItemCellChange(item.checkPosition, index, 'reportField__options', options, false)
        })
      })
      this.currentRulerInfo.rulerItemR.forEach((item, index) => {
        // 如果reportTable有值则更新选项
        item.reportTable && bankReportEnums({
          key: item.reportTable
        }).then(res => {
          const options = res.data[item.reportTable].map((item: EnumBase) => {
            return {
              value: item.key,
              label: item.value,
            }
          }) || []
          // 更新选项
          this.onRulerItemCellChange(item.checkPosition, index, 'reportField__options', options, false)
        })
      })
    },
    /**
     * @description 获取一个期望的规则条目
     * @param { Position } position 左右
     */
    getExpectRulerItem(position: Position): RulerDetailItem {
      const baseItem: any = {
        __id: '' + Math.random(),
        symbol: '',
        symbol__err_msg: '',
        symbol__options: JSON.parse(JSON.stringify(this.symbolOptions)),
        reportTable: '',
        reportTable__err_msg: '',
        reportTable__options: JSON.parse(JSON.stringify(this.reportTableOptions)),
        reportField: '',
        reportField__err_msg: '',
        reportField__options: JSON.parse(JSON.stringify(this.reportFieldOptions)),
        checkPosition: position,
      }

      // 如果是右侧位置，添加清算机构字段
      if (position === 'R') {
        return {
          ...baseItem,
          clearOrgBank: '',
          clearOrgBank__err_msg: '',
          clearOrgBank__options: JSON.parse(JSON.stringify(this.clearOrgBankOptions)),
        } as RulerDetailItem
      }

      return baseItem as RulerDetailItem
    },
    /**
     * 打开弹窗
     * @param { MODAL_TAG } modalTag 要打开哪个modal
     * @param {} modalData 打开是传的数据
     */
    openModal(modalTag: MODAL_TAG, modalData: string = '{}') {
      // 如果modalTag 不是MODAL_TAG类型，不打开modal
      if (!['add', 'edit'].includes(modalTag)) {
        return
      }
      this.modalTag = modalTag
      this.originSelectData = JSON.parse(modalData)
      this.initForm()
      if (modalTag === 'add') {
        // 初始化数据
        this.modalTitle = '新增'
        // 1
      } else if (modalTag === 'edit') {
        // 初始化数据
        this.modalTitle = '编辑'
      } else {
        // 没有对应的modalTag
        // 你不该进来这里
        // this.xxx = true
      }
      this[modalTag + 'ModalVisible'] = true
    },

    /**
     * @description 规则明细变更
     * @param { Position } position 左右
     * @param { number } index 第几行
     * @param { string } column 第几列
     * @param { RulerDetailItem } data 数据
     */
    onRulerItemChange(position: Position, index: number, column: string, data: RulerDetailItem) {
    },
    /**
     * @description 规则明细删除
     * @param { Position } position 左右
     * @param { number } index 第几行
     */
    onRulerItemDel(position: Position, index: number) {
      if (position === 'L') {
        this.currentRulerInfo.rulerItemL.splice(index, 1)
      } else if (position === 'R') {
        this.currentRulerInfo.rulerItemR.splice(index, 1)
      }
    },
    /**
     * @description 规则明细新增
     * @param { Position } position 左右
     * @param { 'add' } type 类型
     */
    onRulerItemAdd(position: Position, type: 'add') {
      if (position === 'L') {
        if (type === 'add') {
          this.currentRulerInfo.rulerItemL.push(this.getExpectRulerItem(position))
        }
      } else if (position === 'R') {
        if (type === 'add') {
          this.currentRulerInfo.rulerItemR.push(this.getExpectRulerItem(position))
        }
      }
    },
    /**
     * @description 规则明细单元格变更
     * @param { Position } position 左右
     * @param { number } index 第几行
     * @param { string } column 第几列
     * @param data 数据
     * @param { boolean } isVal 是否校验
     */
    onRulerItemCellChange(position: Position, index: number, column: string, data: any, isVal: boolean = true) {
      const upData = (position: Position, _column: string, _data: any) => {
        if (position === 'L') {
          this.currentRulerInfo.rulerItemL[index][_column] = JSON.parse(JSON.stringify(_data))
        } else if (position === 'R') {
          this.currentRulerInfo.rulerItemR[index][_column] = JSON.parse(JSON.stringify(_data))
        }
      }
      // 更改目标值
      upData(position, column, data)
      // 涉及联动字段
      if (column === 'reportTable') {
        // TODO
        // 重置reportField值
        upData(position, 'reportField', '')
        // 更新reportField的选项
        bankReportEnums({
          key: data
        }).then(res => {
          const options = res.data[data].map((item: EnumBase) => {
            return {
              value: item.key,
              label: item.value,
            }
          }) || []
          upData(position, 'reportField__options', options)
        })
      }
      // 如果需要该字段校验，调用对应校验函数
      if (!isVal) {
        return
      }
      // 根据位置选择对应的列配置
      const columns = position === 'L' ? this.RulerItemColumnLeft : this.RulerItemColumnRight
      const columnConfig = columns.find(item => item.dataIndex === column)
      const validator = columnConfig && columnConfig.__validator__
      if (validator !== undefined) {
        this.onRulerItemCellValidate(position, index, column, data, validator)
      }
    },

    /**
     * @description 规则明细单元格校验
     * @param { Position } position 左右
     * @param { number } index 第几行
     * @param { string } column 第几列
     * @param { any } data 数据
     * @param { boolean | Validator } validator 校验规则
     */
    onRulerItemCellValidate(position: Position, index: number, column: string, data: any, validator: boolean | Validator) {
      let hasErr = false
      // 如果是boolean类型，定义validator为通用必填校验规则
      if (typeof validator === 'boolean') {
        if (validator) {
          validator = (value: any) => {
            // 校验必填
            // 校验通过返回空字符串，校验不通过返回错误信息
            if (Array.isArray(value) && value.length === 0) {
              hasErr = true
              return '必填'
            }
            if (value === '' || value === null || value === undefined) {
              hasErr = true
              return '必填'
            }
            return ''
          }
        } else {
          if (position === 'L') {
            this.$nextTick(() => {
              this.currentRulerInfo.rulerItemL[index][column + '__err_msg'] = ''
            })
          } else if (position === 'R') {
            this.$nextTick(() => {
              this.currentRulerInfo.rulerItemR[index][column + '__err_msg'] = ''
            })
          }
        }
      }
      // 如果是函数类型，直接调用函数
      if (typeof validator === 'function') {
        const result = validator(data)
        if (result) {
          // 校验不通过
          hasErr = true
          if (position === 'L') {
            this.$nextTick(() => {
              this.currentRulerInfo.rulerItemL[index][column + '__err_msg'] = result
            })
          } else if (position === 'R') {
            this.$nextTick(() => {
              this.currentRulerInfo.rulerItemR[index][column + '__err_msg'] = result
            })
          }
        } else {
          // 校验通过
          hasErr = false
          if (position === 'L') {
            this.$nextTick(() => {
              this.currentRulerInfo.rulerItemL[index][column + '__err_msg'] = ''
            })
          } else if (position === 'R') {
            this.$nextTick(() => {
              this.currentRulerInfo.rulerItemR[index][column + '__err_msg'] = ''
            })
          }
        }
      }
      return hasErr
    },

    /**
     * @description 规则明细全校验
     */
    onRulerItemValidate() {
      let hasErr = false
      // 遍历左侧规则
      this.currentRulerInfo.rulerItemL.forEach((item, index) => {
        // 遍历每一列
        this.RulerItemColumnLeft.forEach(column => {
          // 如果有校验规则
          if (column.__validator__) {
            // 调用校验函数，使用 || 来累积错误状态
            const currentErr = this.onRulerItemCellValidate('L', index, column.dataIndex, item[column.dataIndex], column.__validator__)
            hasErr = hasErr || currentErr
          }
        })
      })
      // 遍历右侧规则
      this.currentRulerInfo.rulerItemR.forEach((item, index) => {
        // 遍历每一列
        this.RulerItemColumnRight.forEach(column => {
          // 如果有校验规则
          if (column.__validator__) {
            // 调用校验函数，使用 || 来累积错误状态
            const currentErr = this.onRulerItemCellValidate('R', index, column.dataIndex, item[column.dataIndex], column.__validator__)
            hasErr = hasErr || currentErr
          }
        })
      })
      return hasErr
    },
    /** 表单提交 */
    onModalSubmit() {
      // 校验数据
      this.$refs.rulerForm.validate((valid: boolean) => {
        if (valid) {
          // 校验取数逻辑
          const hasErr = this.onRulerItemValidate()
          if (!hasErr) {
            // 深拷贝参数对象，避免修改原始数据
            const geneOperateRulerParam = {
              ...this.geneOperateRulerParam,
              detailInfoReqVoList: this.geneOperateRulerParam.detailInfoReqVoList.map((item: any) => {
                const newItem = { ...item }
                // 为左侧规则项添加clearOrgBank字段
                if (item.checkPosition === 'L') {
                  newItem.clearOrgBank = this.geneOperateRulerParam.clearOrgBank
                }
                return newItem
              })
            }
            // 提交数据
            this.isSubmitIng = true
            this.operaRulerApi(geneOperateRulerParam).then((res: any) => {
              if (res.code === 200) {
                this.$message.success(this.modalTag === 'add' ? '新增成功' : '编辑成功')
                this.onModalClose()
                // TODO 低代码刷新列表
                // 运行低代码列表查询接口
                this.reportCheckRuler.runQuery('getPage')
              } else {
                this.$message.error(res.message)
              }
            }, (res) => {
              this.$message.error(res.message)
            }).finally(() => {
              this.isSubmitIng = false
            })
          }
        }
      })
    },
    /**
     * @description 关闭弹窗
     */
    onModalClose() {
      // 清除验证
      this.$refs.rulerForm.clearValidate()
      if (this.modalTag === 'add') {
        this.addModalVisible = false
      } else if (this.modalTag === 'edit') {
        this.editModalVisible = false
      }
    },
  },
  async mounted() {
    this.schema = await fetchSchema(this.pageCode)
    bankReportEnums().then(res => {
      this.clearOrgBankOptions = res.data.organEnum.map((item: EnumBase) => {
        return {
          value: item.key,
          label: item.value,
        }
      }) || []
      this.statusOptions = res.data.statusEnum.map((item: EnumBase) => {
        return {
          value: item.key,
          label: item.value,
        }
      }) || []
      this.reportTableOptions = (res.data.reportFileTypeEnum.map((item: EnumBase) => {
        return {
          value: item.key,
          label: item.value,
        }
      }) || []).sort((a, b) => {
        return a.label.localeCompare(b.label, 'zh-Hans-CN', { numeric: true })
      })
    })
  },
})
</script>
<template>
  <div class="p-5">
    <low-code-renderer v-if="schema.components" id="reportCheckRuler" :schema="schema" @openModal="openModal"/>
    <a-drawer
      :title="modalTitle"
      placement="right"
      :visible="addModalVisible||editModalVisible"
      :width="drawerModalWidth"
      @close="onModalClose"
    >
      <a-spin :spinning="isSubmitIng" style="position: initial;">
        <a-form-model ref="rulerForm" class="ruler-form" :model="rulerForm" :rules="rules" v-bind="layout">
          <a-form-model-item label="清算机构" prop="clearOrgBank">
            <a-radio-group v-model="rulerForm.clearOrgBank" :options="clearOrgBankOptions" :disabled="modalTag==='edit'"/>
          </a-form-model-item>
          <a-form-model-item label="状态" prop="status">
            <a-radio-group v-model="rulerForm.status" :options="statusOptions"/>
          </a-form-model-item>
          <!-- 规则配置 -->
          <a-table
            class="ruler-table"
            :rowKey="'checkMethod'"
            :dataSource="[currentRulerInfo]"
            :columns="RulerDetailColumn"
            :pagination="false"
            size="small"
            :bordered="true"
          >
            <!-- 左侧取数逻辑  -->
            <template slot="rulerItemL" slot-scope="text">
              <ruler-item
                :dataSource="text"
                :rowKey="'__id'"
                size="small"
                :columns="RulerItemColumnLeft"
                :skipCheckFields="['__id', 'ruleId', 'checkPosition']"
                :show-header="true"
                @rowDel="(index)=>{onRulerItemDel('L', index)}"
                @rowAdd="(type)=>{onRulerItemAdd('L', type)}"
                @cellChange="(index, column, data)=>{onRulerItemCellChange('L', index, column, data)}"
              ></ruler-item>
            </template>
            <template slot="checkMethod">
              <a-select :value="currentRulerInfo.checkMethod"">
                <a-select-option value="=">=</a-select-option>
              </a-select>
            </template>
            <!-- 右侧取数逻辑  -->
            <template slot="rulerItemR" slot-scope="text">
              <ruler-item
                :dataSource="text"
                :rowKey="'__id'"
                size="small"
                :columns="RulerItemColumnRight"
                :skipCheckFields="['__id', 'ruleId', 'checkPosition']"
                :show-header="true"
                @rowDel="(index)=>{onRulerItemDel('R', index)}"
                @rowAdd="(type)=>{onRulerItemAdd('R', type)}"
                @cellChange="(index, column, data)=>{onRulerItemCellChange('R', index, column, data)}"
              ></ruler-item>
            </template>
          </a-table>
        </a-form-model>
        <div
          :style="{
            position: 'absolute',
            right: 0,
            bottom: 0,
            width: '100%',
            borderTop: '1px solid #e9e9e9',
            padding: '10px 16px',
            background: '#fff',
            textAlign: 'right',
            zIndex: 1,
          }"
        >
          <a-button :style="{ marginRight: '8px' }" @click="onModalClose">
            取消
          </a-button>
          <a-button type="primary" @click="onModalSubmit">
            确认
          </a-button>
        </div>
      </a-spin>
    </a-drawer>
  </div>
</template>
<style lang="less" scoped>

.ruler-form {
  ::v-deep .ant-form-item {
    margin-bottom: 0;
  }
}

.ruler-table {
  ::v-deep .ant-table-tbody > tr:hover:not(.ant-table-expanded-row):not(.ant-table-row-selected) > td {
    background: #ffffff;
  }
  ::v-deep .ant-table-tbody > tr > td {
    vertical-align: top;
  }
}

::v-deep .ant-spin-container {
  position: initial !important;
}
</style>
