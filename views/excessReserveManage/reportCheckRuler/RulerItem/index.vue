<template>
  <div class="ruler-item-container">
    <a-table
      :rowKey="rowKey"
      :dataSource="dataSource"
      :columns="columns"
      :pagination="pagination"
      :size="size"
      :show-header="showHeader"
      :bordered="bordered"
    >
      <template v-for="specialKey in specialKeys" :slot="specialKey" slot-scope="text, record, index">
        <a-select
          v-if="record.hasOwnProperty(specialKey + '__options')"
          :key="specialKey + '__'"
          :class="record[specialKey+'__err_msg']?'err':''"
          :value="text"
          :getPopupContainer="triggerNode => triggerNode.parentNode"
          :options="record[specialKey + '__options']"
          @change="(value) => onSelectChange(index, specialKey, value)"
        >
        </a-select>
        <span v-else :key="specialKey + '__'">{{ text }}</span>
        <div v-if="record[specialKey+'__err_msg']" :key="specialKey+'__err_info'" class="err-msg">
          {{ record[specialKey+'__err_msg'] || ' ' }}
        </div>
      </template>
      <template slot="action" slot-scope="text, record, index">
        <!-- TODO 这个id判断需要优化 -->
        <a-popconfirm title="是否移除?" :okText="'是'" :cancelText="'否'" @confirm="() => onRowDel(index)">
          <a href="javascript:;">移除</a>
        </a-popconfirm>
      </template>
      <!-- 底部新增 -->
      <template slot="footer">
        <div class="sub-footer-invoice">
          <a-button type="dashed" :block="true" icon="plus" style="opacity: 0.8" @click="onRowAdd">新增</a-button>
        </div>
      </template>
    </a-table>
  </div>
</template>

<script>
import { defineComponent } from '@vue/composition-api'

export default defineComponent({
  name: 'RulerItem',
  props: {
    rowKey: {
      type: String,
      default: 'id',
      require: true,
    },
    dataSource: {
      type: Array,
      default: () => [],
      require: true,
    },
    size: {
      type: 'default' | 'middle' | 'small',
      default: 'default',
    },
    columns: {
      type: Array,
      default: () => [],
      require: true,
    },
    pagination: {
      type: Boolean,
      default: false,
    },
    showHeader: {
      type: Boolean,
      default: true,
    },
    bordered: {
      type: Boolean,
      default: false,
    },
    skipCheckFields: {
      type: Array,
      default: () => [],
    },
  },
  data() {
    return {
      _dataSource: [],
    }
  },
  watch: {
    // 监听this.dataSource的变化
    dataSource: {
      handler(val) {
        this._dataSource = JSON.parse(JSON.stringify(val))
      },
      immediate: true,
      deep: true,
    }
  },
  computed: {
    specialKeys() {
      // 获取以__options结尾的字段,
      // 然后把__options去掉,
      // 然后把剩下的字段返回
      // const specialKeys = Array.isArray(this.dataSource) && this.dataSource[0] && Object.keys(this.dataSource[0]).filter(key => key.endsWith('__options')).map(key => key.replace(/__options$/, ''))
      const specialKeys = this.columns.filter(column => !['action'].includes(column.dataIndex)).map(column => column.dataIndex)
      return specialKeys
    }
  },
  methods: {
    onRowDel(index) {
      if (index === 0) {
        this.$message.warn('至少存在一条取数逻辑')
        return
      }
      this.$emit('rowDel', index)
    },
    onRowAdd() {
      // 如果刚加的一条数据每一个字段都没有值，不允许新增
      const lastItem = this._dataSource[this._dataSource.length - 1]
      if (!lastItem) {
        this.$emit('rowAdd', 'add')
        return
      }
      const skipCheckFields = this.skipCheckFields
      // 获取需要检查的字段
      const checkFields = Object.keys(lastItem).filter(key => !skipCheckFields.includes(key) && !key.endsWith('__options') && !key.endsWith('__err_msg'))
      // 检查是否可以新增
      // 如果最后一条数据的所有需检查字段都为空，canAdd为false，否则为true
      const canAdd = checkFields.some(key => {
        const value = lastItem[key]
        if (typeof value === 'string') {
          return value.trim().length > 0
        }
        if (Array.isArray(value)) {
          return value.length > 0
        }
        // 既不是数组也不是字符串，直接返回false
        return false
      })
      if (canAdd) {
        this.$emit('rowAdd', 'add')
      }
    },
    /** 选择框改变 */
    onSelectChange(index, column, value) {
      this.$emit('cellChange', index, column, value)
    },
  },
  created() {
  },
  mounted() {
  }
})
</script>

<style lang="less" scoped>
.ruler-item-container {
  ::v-deep .ant-table-tbody > tr:not(:last-child) > td {
    border-bottom: 1px solid #e8e8e8 !important;
  }
  ::v-deep .ant-table-tbody > tr > td {
    border-right: 0;
    vertical-align: middle !important;
  }
  ::v-deep .ant-table-bordered .ant-table-tbody > tr > td {
    border-right: 1px solid #e8e8e8;
  }
}
.err{
  ::v-deep .ant-select-selection {
    border: 1px solid red;
  }
}
.err-msg{
  font-size: 12px;
  text-align: left;
  color: red;
}
</style>
