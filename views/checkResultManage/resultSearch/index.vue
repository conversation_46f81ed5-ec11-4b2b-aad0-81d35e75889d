<script lang="ts">
import { defineComponent } from '@vue/composition-api'
import { fetchSchema } from '@/utils/lowcode'
export default defineComponent({
  data() {
    return {
      pageCode: 'checkResultSearch',
      schema: {} as any
    }
  },
  async mounted() {
    this.schema = await fetchSchema(this.pageCode)
  },
  methods: {
    pageTo(path:string) {
      this.$router.push(path)
    }
  }
})
</script>
<template>
  <div class="p-5">
    <low-code-renderer v-if="schema.components" :schema="schema" @pageTo="pageTo"/>
  </div>
</template>
