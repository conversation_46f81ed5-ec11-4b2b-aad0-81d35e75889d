<script lang="ts">
import { defineComponent } from '@vue/composition-api'
import { fetchSchema } from '@/utils/lowcode'
export default defineComponent({
  data() {
    return {
      pageCode: 'cfsAccountingResult',
      schema: {} as any
    }
  },
  async mounted() {
    this.schema = await fetchSchema(this.pageCode)
  },
  methods: {
    /**
     * @description 跳转详情页
     * @param paramsJSON 跳转详情页所需参数
     */
    async jumpDetail(paramsJSON: string) {
      const params = JSON.parse(paramsJSON)
      this.$router.push({
        path: '/checkResultManage/cfsAccountingDetail',
        query: {
          ...params
        }
      })
    }
  }
})
</script>
<template>
  <div class="p-5">
    <low-code-renderer v-if="schema.components" :schema="schema" @jumpDetail="jumpDetail"/>
  </div>
</template>
