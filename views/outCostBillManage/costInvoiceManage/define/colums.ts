type Column = {
  title: string
  dataIndex: string
  key: string
  align?: string
  width?: number
  /**
   * When using columns, you can use this property to configure the properties that support the slot-scope,
   * such as scopedSlots: { customRender: 'XXX'}
   * @type object
   */
  scopedSlots?: object
}

// 子账单的付款记录列
export const PaidRecordColumns: Column[] = [
  {
    title: '收款方名称',
    dataIndex: 'bankCode',
    width: 120,
    key: 'bankCode',
    scopedSlots: { customRender: 'bankCode' }
  },
  {
    title: '业务类型',
    dataIndex: 'businessType',
    key: 'businessType',
    width: 85,
    scopedSlots: { customRender: 'businessType' }
  },
  {
    title: '卡类型',
    dataIndex: 'cardType',
    key: 'cardType',
    width: 60,
    scopedSlots: { customRender: 'cardType' }
  },
  {
    title: '付款日期',
    dataIndex: 'paymentTime',
    key: 'paymentTime',
    width: 90,
    scopedSlots: { customRender: 'paymentTime' }
  },
  {
    title: '应收发票金额(元)',
    dataIndex: 'receivableInvoiceAmount',
    key: 'receivableInvoiceAmount',
    width: 150,
    align: 'right',
    scopedSlots: { customRender: 'receivableInvoiceAmount' }
  },
  {
    title: '实收发票金额(元)(含税)',
    dataIndex: 'invoiceAmount',
    key: 'invoiceAmount',
    width: 150,
    align: 'right',
    scopedSlots: { customRender: 'invoiceAmount' }
  },
  {
    title: '实收税额(元)',
    dataIndex: 'taxAmount',
    key: 'taxAmount',
    width: 120,
    align: 'right',
    scopedSlots: { customRender: 'taxAmount' }
  }
]

/** 所关联发票的表格列 */
export const InvoiceColumns: Column[] = [
  {
    title: '发票号',
    dataIndex: 'realInvoiceNo',
    key: 'realInvoiceNo',
    width: 140,
    scopedSlots: { customRender: 'realInvoiceNo' }
  },
  {
    title: '发票金额(元)',
    dataIndex: 'realInvoiceAmount',
    key: 'realInvoiceAmount',
    width: 140,
    align: 'right',
    scopedSlots: { customRender: 'realInvoiceAmount' }
  },
  {
    title: '实收发票金额(元)(含税)',
    dataIndex: 'invoiceAmount',
    key: 'invoiceAmount',
    width: 140,
    align: 'right',
    scopedSlots: { customRender: 'invoiceAmount' }
  },
  {
    title: '实收税额(元)',
    dataIndex: 'taxAmount',
    key: 'taxAmount',
    width: 140,
    align: 'right',
    scopedSlots: { customRender: 'taxAmount' }
  },
  {
    title: '操作',
    dataIndex: 'action',
    key: 'action',
    width: 60,
    align: 'center',
    scopedSlots: { customRender: 'action' }
  }
]
