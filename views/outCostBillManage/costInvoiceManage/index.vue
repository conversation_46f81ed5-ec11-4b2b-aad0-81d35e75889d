<template>
  <div class="p-5">
    <low-code-renderer v-if="schema.components" id="costInvoiceManage" :schema="schema" @openUploadModal="openUploadModal"/>
    <a-drawer
      title="上传发票"
      :visible="showUploadModal"
      :destroyOnClose="true"
      :mask-closable="false"
      width="80%"
      @close="showUploadModal = false">
      <a-spin :spinning="uploadFileIng">
        <a-form-model ref="uploadInvoiceForm" :rules="InvoiceRules" v-bind="InvoiceFormLayout">
          <a-form-model-item label="选中数据汇总" class="sum-items">
            <a-descriptions :column="2">
              <a-descriptions-item label="应收发票金额(元)" :span="2">
                {{ totalReceivableInvoice }}
              </a-descriptions-item>
              <a-descriptions-item label="实收发票金额(元)">
                {{ totalActualInvoice }}
              </a-descriptions-item>
              <a-descriptions-item label="税额(元)">
                {{ totalTax }}
              </a-descriptions-item>
            </a-descriptions>
          </a-form-model-item>
          <a-form-model-item label="上传发票">
            <a-upload
              name="files"
              :action="getUploadPath"
              :headers="headers"
              :multiple="true"
              :accept="accept"
              list-type="picture-card"
              :file-list="fileList"
              :remove="handleRemove"
              @preview="handlePreview"
              @change="handleChange"
            >
              <div v-if="fileList.length < 8">
                <a-icon type="plus" />
                <div class="ant-upload-text">
                </div>
              </div>
            </a-upload>
          </a-form-model-item>
          <a-form-model-item label="发票号" class="sum-items">
            <a-tag
              v-for="invoice in invoiceTags"
              :key="invoice"
            >
              {{ invoice }}
            </a-tag>
          </a-form-model-item>
          <a-form-model-item label="关联发票">
            <a-table
              :columns="columns"
              :data-source="selectedPaidData"
              size="small"
              :pagination="false"
              :rowKey="'id'"
              :expandedRowRender="expandedRowRender"
            >
              <template slot="receivableInvoiceAmount" slot-scope="text">
                <span>
                  {{ toThousands( Number( text || 0).toFixed(2) ) }}
                </span>
              </template>
              <template slot="bankCode" slot-scope="text">
                <span>
                  {{ valueToLabel(text, 'PAYER_NAME') }}
                </span>
              </template>
              <template slot="businessType" slot-scope="text">
                <span>
                  {{ valueToLabel(text, 'INVOICE_BUSS_TYPE') }}
                </span>
              </template>
              <template slot="cardType" slot-scope="text">
                <span>
                  {{ valueToLabel(text, 'INVOICE_CARD_TYPE') }}
                </span>
              </template>
              <template slot="invoiceAmount" slot-scope="text">
                <span>
                  {{ toThousands( Number( text || 0).toFixed(2) ) }}
                </span>
              </template>
              <template slot="taxAmount" slot-scope="text">
                <span>
                  {{ toThousands( Number( text || 0).toFixed(2) ) }}
                </span>
              </template>
            </a-table>
          </a-form-model-item>
        </a-form-model>
      </a-spin>
      <div class="drawer-footer">
        <a-button @click="cancleUpload">
          取消
        </a-button>
        <a-button style="margin-left: 10px;" :loading="submitIng" :disabled="!canSubmit" type="primary" @click="submitUploadDebounce">
          确认
        </a-button>
      </div>
    </a-drawer>
    <a-modal :visible="previewVisible" :footer="null" @cancel="handleCancel">
      <img alt="example" style="width: 100%" :src="previewImage" />
    </a-modal>
  </div>
</template>

<script lang="ts">
import { defineComponent } from '@vue/composition-api'
import { loadSchema } from '@yeepay/lowcode-renderer'
import { getLabelFromEnum, toThousands } from '_/utils'
import SubTable from './SubTable.vue'
import { selectData } from './mock'
import { PaidRecordColumns } from './define/colums'
import Decimal from 'decimal.js'
import numeral from 'numeral'
import { CostInvoiceType, InvoiceDetailType } from '_/types/invoiceManage'
import Cookies from 'js-cookie'
import { getInvoiceDetail, uploadInvoice } from '_/api/costInvoice'
import { CostLinkInvoiceType, InvoiceDetail, InvoiceDetailRes, UploadInvoiceReq, UseInvoiceType } from '_/api/types/costInvoiceManage'
import { debounce } from 'radash'
import { Modal } from 'ant-design-vue'
export default defineComponent({
  components: {
    SubTable
  },
  data() {
    // const _selectData = JSON.parse(JSON.stringify(selectData)) as CostInvoiceType[]
    const _selectData = [] as CostInvoiceType[]
    return {
      pageCode: 'costInvoiceManage',
      schema: {} as any,
      /** 千分位转换 */
      toThousands,
      /** 获取发票详情接口 */
      getInvoiceDetail,
      /** 是否显示上传发票弹窗 */
      showUploadModal: false,
      /** 提交中 */
      submitIng: false,
      /** 选中的已付款原始数据 */
      selectedPaidOriginData: selectData,
      /** 选中的已付款数据 */
      selectedPaidData: _selectData,
      /** 已付款数据表格列 */
      columns: PaidRecordColumns,
      /** 上传发票表单校验规则 */
      InvoiceRules: {

      },
      /** 上传发票表单布局 */
      InvoiceFormLayout: {
        labelCol: { span: 3 },
        wrapperCol: { span: 21 }
      },
      /** 上传文件大小限制xM */
      maxSize: 5,
      /** 支持的文件类型 pdf、odf、jpeg、jpg、png */
      accept: 'image/*,.pdf,.odf',
      /** 上传接口使用的header */
      headers: {
        Authorization: Cookies.get('TOKEN')
      },
      fileList: [],
      uploadFileIng: false,
      previewVisible: false,
      previewImage: '',
      /** 发票信息列表 */
      invoiceList: [] as InvoiceDetail[],
      /** 发票关联记录映射 - 记录每张发票被哪些付款记录使用 */
      invoiceUsageMap: {} as { [realInvoiceNo: string]: Array<{ invoiceNo: string, detailIndex: number, invoiceAmount: number, taxAmount: number }> },
      rules: {
        invoiceAmount: (value: number, realInvoiceNo: string, currentInvoiceNo?: string, currentDetailIndex?: number) => {
          if (!value) {
            return '发票金额不能为空'
          }
          if (value < 0) {
            return '发票金额不能小于0'
          }
          // 使用新的剩余金额计算方法
          const remainAmount = this.calculateInvoiceRemainAmount(realInvoiceNo, currentInvoiceNo, currentDetailIndex)
          if (value > remainAmount) {
            return `发票金额不能大于发票剩余金额(${remainAmount.toFixed(2)})`
          }

          // 校验列表中的sum（实收发票金额）不能大于上传发票的发票金额
          const totalUsedAmount = this.calculateTotalUsedInvoiceAmount(realInvoiceNo, currentInvoiceNo, currentDetailIndex)
          const invoice = this.invoiceList.find((inv: InvoiceDetail) => inv.realInvoiceNo === realInvoiceNo)
          if (invoice) {
            const totalAmount = Number(new Decimal(invoice.usedInvoiceAmount || 0).add(new Decimal(totalUsedAmount)).add(new Decimal(value)).valueOf())
            if (totalAmount > invoice.realInvoiceAmount) {
              return '录入的实收发票金额 大于 已上传发票金额，请检查！'
            }
          }

          return ''
        },
        taxAmount: (value: any, realInvoiceNo: string, currentInvoiceNo?: string, currentDetailIndex?: number) => {
          if (value === null || value === undefined || value === '') {
            return '发票税额不能为空'
          }
          const numValue = Number(value)
          if (isNaN(numValue) || numValue < 0) {
            return '发票税额不能小于0'
          }
          // 使用新的剩余税额计算方法
          const remainAmount = this.calculateInvoiceRemainTaxAmount(realInvoiceNo, currentInvoiceNo, currentDetailIndex)
          if (numValue > remainAmount) {
            return `发票税额不能大于剩余税额(${remainAmount.toFixed(2)})`
          }

          // 校验列表中的sum（税额）不能大于上传发票的税额
          const totalUsedTaxAmount = this.calculateTotalUsedTaxAmount(realInvoiceNo, currentInvoiceNo, currentDetailIndex)
          const invoice = this.invoiceList.find((inv: InvoiceDetail) => inv.realInvoiceNo === realInvoiceNo)
          if (invoice) {
            const totalTaxAmount = Number(new Decimal(invoice.usedTaxAmount || 0).add(new Decimal(totalUsedTaxAmount)).add(new Decimal(numValue)).valueOf())
            if (totalTaxAmount > invoice.realTaxAmount) {
              return '录入的税额 大于 已上传发票税额，请检查！'
            }
          }

          return ''
        }
      } as { [key: string]: (value: number, realInvoiceNo: string, currentInvoiceNo?: string, currentDetailIndex?: number) => string },
    }
  },
  computed: {
    /** 低代码关联 */
    costInvoiceManage() {
      return (this as any).__yeepay_lowcode_costInvoiceManage__.value
    },
    /** 发票tag */
    invoiceTags() {
      return this.invoiceList.map((item: InvoiceDetail) => item.realInvoiceNo)
    },
    /** 应收发票汇总 */
    totalReceivableInvoice() {
      return numeral(this.selectedPaidData.reduce((prev: any, cur: any) => prev.plus(new Decimal(cur.receivableInvoiceAmount)), new Decimal(0))).format('0,0.00')
    },
    /** 实收发票汇总 */
    totalActualInvoice() {
      return numeral(this.selectedPaidData.reduce((prev: any, cur: any) => prev.plus(new Decimal(cur.invoiceAmount)), new Decimal(0))).format('0,0.00')
    },
    /** 税额汇总 */
    totalTax() {
      return numeral(this.selectedPaidData.reduce((prev: any, cur: any) => prev.plus(new Decimal(cur.taxAmount)), new Decimal(0))).format('0,0.00')
    },
    /** 上传发票入参 */
    uploadInvoiceParams() {
      const allSelect = this.selectedPaidData.map((item: CostInvoiceType) => {
        const _details = item.details.filter((detail: InvoiceDetailType) => !detail.id && detail.realInvoiceNo) || []
        return {
          invoiceNo: item.invoiceNo,
          // 添加新的关联发票
          details: _details.map((detail: InvoiceDetailType) => {
            // 从发票文件列表中找到对应的发票OSS信息
            const file: any = this.fileList.find((file: any) => file.realInvoiceNo === detail.realInvoiceNo)
            return {
              realInvoiceNo: detail.realInvoiceNo,
              invoiceAmount: detail.invoiceAmount,
              taxAmount: detail.taxAmount,
              invoiceInfo: JSON.stringify(file ? file.response.data[0] : '')
            }
          })
        }
      })
      return allSelect.filter((item: any) => item.details.length > 0)
    },
    canSubmit() {
      return this.uploadInvoiceParams.some((item: CostLinkInvoiceType) => {
        return item.details.length > 0
      })
    },
    /** 上传发票接口 */
    submitUploadDebounce() {
      return debounce({
        delay: 400
      }, this.submitUpload)
    },
    // 创建发票号到发票对象的映射
    invoiceMap() {
      const map = {} as any
      this.invoiceList.forEach((invoice: any) => {
        map[invoice.realInvoiceNo] = {
          ...invoice,
          remainInvoiceAmount: invoice.remainInvoiceAmount,
          remainTaxAmount: invoice.remainTaxAmount
        }
      })
      return map
    }
  },

  async mounted() {
    this.schema = await loadSchema({
      code: this.pageCode
    })
  },

  methods: {
    /** 初始化文件上传组件并重置修改 */
    initUpload() {
      this.fileList = []
      this.invoiceList = []
      // 清理发票使用映射
      this.invoiceUsageMap = {}
    },

    /** 初始化上传发票弹窗 */
    initUploadModal(originData: CostInvoiceType[]) {
      // 初始化数据
      this.selectedPaidOriginData = JSON.parse(JSON.stringify(originData))
      this.selectedPaidData = JSON.parse(JSON.stringify(this.selectedPaidOriginData)).map((item: CostInvoiceType) => {
        item.details = item.details || []
        return item
      })
      this.initUpload()
      this.showUploadModal = true

      // 重置发票状态并初始化
      this.resetAndInitInvoiceState()
    },

    /** 打开上传发票弹窗 */
    openUploadModal(originDataStr: string) {
      const originData: CostInvoiceType[] = JSON.parse(originDataStr) || []
      originData.length > 0 ? this.initUploadModal(originData) : this.$message.error('未选择数据')
    },

    /**
     * @description 文件对象转blob对象
     * @param file 文件对象
     */
    fileToBlob(file: any) {
      return new Blob([file], { type: file.type })
    },

    /** 获取文件上传请求地址 */
    getUploadPath() {
      return '/zjpt-boss/main/uploadFile'
    },

    /** 预览逻辑 */
    async handlePreview(file: any) {
      // if (!file.url && !file.preview) {
      //   file.preview = await getBase64(file.originFileObj)
      // }
      // this.previewImage = file.url || file.preview
      const url = URL.createObjectURL(this.fileToBlob(file.originFileObj))
      // this.previewImage = url
      // this.previewVisible = true
      window.open(url)
    },

    /** 文件上传前限制大小 */
    beforeUpload(file: any) {
      const imgSize = file.size / 1024 / 1024
      if (imgSize > this.maxSize) {
        this.$message.error(`图片需${this.maxSize}M以内!`)
        this.remove(file.uid)
        return false
      }
      // const validTypes = ['image/jpeg', 'image/png', 'image/bmp']
      // if (!validTypes.includes(file.type)) {
      //   this.$message.error('所选格式不支持')
      //   this.remove(file.uid)
      //   return false
      // }
      return true
    },

    handleRemove(file: any) {
      const isUsed = !!this.uploadInvoiceParams.find((item: CostLinkInvoiceType) => {
        return !!(item.details.find((detail: UseInvoiceType) => detail.realInvoiceNo === file.realInvoiceNo))
      })
      if (isUsed) {
        Modal.confirm({
          title: '提示',
          content: '该发票已被使用，确定删除吗？',
          onOk: () => {
            this.remove(file.uid)
            this.removeInvoiceList(file.realInvoiceNo)
            this.removeInvoice(file.realInvoiceNo)
            // 清理发票使用映射
            this.cleanInvoiceUsageMap(file.realInvoiceNo)
          }
        })
      }
      return !isUsed
    },

    /** 处理文件上传后逻辑 */
    handleChange(files: any) {
      this.fileList = files.fileList
      // 处理文件上传后接口响应数据
      if (files.file.status === 'done' && files.file.response) {
        this.uploadFileIng = true
        const { code, data } = files.file.response
        const _data: any = {}
        _data.uploadFileVos = data
        if (code === 200) {
          this.$message.success('上传成功，正在获取发票信息...')
          this.getInvoiceDetail(_data).then((res: any) => {
            const { code, data } = res as InvoiceDetailRes
            const newInvoiceNo = data[0].realInvoiceNo

            // 检查是否存在重复发票号
            if (this.invoiceTags.includes(newInvoiceNo)) {
              this.$message.info('存在相同发票号的发票，请重新上传')
              this.remove(files.file.uid)
            } else {
              // 将发票号塞入fileList中作为索引
              const file = files.fileList.find((item: any) => item.uid === files.file.uid)
              if (file) {
                file.realInvoiceNo = newInvoiceNo
              }
              // 替换发票列表里对应的发票信息
              data.forEach((item: InvoiceDetail) => {
                // 计算剩余金额，安全计算
                item.remainInvoiceAmount = Number(new Decimal(item.realInvoiceAmount).sub(new Decimal(item.usedInvoiceAmount)).valueOf())
                item.remainTaxAmount = Number(new Decimal(item.realTaxAmount).sub(new Decimal(item.usedTaxAmount)).valueOf())

                // 这里把默认值都存到对象里
                const defaultItem = { ...item }
                item.defaultItem = defaultItem

                const index = this.invoiceList.findIndex((invoice: InvoiceDetail) => invoice.realInvoiceNo === item.realInvoiceNo)
                index > -1 ? this.invoiceList.splice(index, 1, item) : this.invoiceList.push(item)
              })
            }
          }, res => {
            this.remove(files.file.uid)
            this.$message.error(res.message || '发票信息获取失败')
          }).finally(() => {
            this.uploadFileIng = false
          })
        } else {
          this.$message.error('上传失败')
          this.uploadFileIng = false
        }
      } else if (files.file.status === 'removed') {
        // 立即清理，不使用 nextTick 避免时序问题
        this.removeInvoiceList(files.file.realInvoiceNo)
        this.removeInvoice(files.file.realInvoiceNo)
        this.cleanInvoiceUsageMap(files.file.realInvoiceNo)
      }
    },

    /** 移除发票 */
    removeInvoiceList(realInvoiceNo: string) {
      if (!realInvoiceNo) return
      this.invoiceList = this.invoiceList.filter((item: InvoiceDetail) => item.realInvoiceNo !== realInvoiceNo)
    },

    /**
     * @description 根据uid移除filelist中的文件
     * @param uid 文件uid
     */
    remove(uid: string) {
      if (!uid) return
      this.fileList = this.fileList.filter((item: any) => item.uid !== uid)
    },

    /**
     * @description 根据真实发票号，将成本付款记录关联的新增发票移除
     * @param realInvoiceNo 真实发票号
     */
    removeInvoice(realInvoiceNo: string) {
      if (!realInvoiceNo) return
      this.selectedPaidData.forEach((item: CostInvoiceType) => {
        item.details = item.details.filter((detail: InvoiceDetailType) => detail.id || detail.realInvoiceNo !== realInvoiceNo)
      })
    },
    /**
     * 重新计算同一发票涉及的金额/税额
     */
    // recalculateAmount(){

    // },
    /**
     * @description 修改付款记录的关联的发票列表
     * @param invoiceNo 付款记录的发票关联主键
     * @param dataIndex 修改的字段
     * @param value 修改的值
     */
    onCostInvoiceTableChange(invoiceNo: any, dataIndex: keyof CostInvoiceType, value: any) {
      const dataSource = [...this.selectedPaidData]
      console.log('onCostInvoiceTableChange________dataSource', dataSource)
      console.log('invoiceNo', invoiceNo)
      console.log('dataIndex', dataIndex)

      // let tableItem // 一级关联发票（表格list）
      // let invoiceList // 二级发票列表
      // // let tableItem = '' //
      // let aa // 剩余发票金额
      // dataSource.filter((item) => {
      //   tableItem = item
      //   invoiceList = item.details
      // })

      const target = dataSource.find(item => item.invoiceNo === invoiceNo)
      if (target) {
        (target[dataIndex] as any) = value
        this.selectedPaidData = dataSource
      }
    },

    /** 展开行渲染 */
    expandedRowRender(record: CostInvoiceType, index: number) {
      return this.$createElement(SubTable, {
        props: {
          parentRecord: record,
          parentIndex: index,
          invoiceMap: this.invoiceMap,
          /** 全局已使用的发票映射 */
          invoiceUsageMap: this.invoiceUsageMap,
          /** 可以选择的经过验证真伪的发票 */
          options: this.invoiceList.filter((item: InvoiceDetail) => {
            // 只基于数据库状态过滤，不考虑当前编辑状态
            const dbRemainAmount = Number(new Decimal(item.realInvoiceAmount || 0).sub(new Decimal(item.usedInvoiceAmount || 0)).valueOf())
            const dbRemainTax = Number(new Decimal(item.realTaxAmount || 0).sub(new Decimal(item.usedTaxAmount || 0)).valueOf())
            return dbRemainAmount > 0 || dbRemainTax > 0
          }).map((item: InvoiceDetail) => {
            return {
              label: item.realInvoiceNo,
              value: item.realInvoiceNo,
              ...item
            }
          }),
          // /** 可以选择的经过验证真伪的发票（过滤掉已完全使用的发票） */
          // options: this.invoiceList.filter((item: InvoiceDetail) => {
          //   // 过滤掉已完全使用的发票（剩余金额和剩余税额都为0或负数）
          //   return item.remainInvoiceAmount > 0 || item.remainTaxAmount > 0
          // }).map((item: InvoiceDetail) => {
          //   return {
          //     label: item.realInvoiceNo,
          //     value: item.realInvoiceNo,
          //     ...item
          //   }
          // }),
          /** 传递父组件的校验方法 */
          validateInvoiceAmount: this.validateInvoiceAmount,
          validateTaxAmount: this.validateTaxAmount,
          /** 传递父组件的剩余金额计算方法 */
          calculateInvoiceRemainAmount: this.calculateInvoiceRemainAmount,
          calculateInvoiceRemainTaxAmount: this.calculateInvoiceRemainTaxAmount,
        },
        on: {
          tableChange: (invoiceNo: string, value: InvoiceDetail[]) => this.onSubTableChange(invoiceNo, value),
          calculateUsage: () => this.handleCalculateUsage(),
        },
      })
    },

    /**
     * @description 修改附件记录的发票信息（简化版本）
     * @param invoiceNo 付款记录对应的发票号主键
     * @param value 修改的值
     */
    onSubTableChange(invoiceNo: string, value: InvoiceDetail[]) {
      // 1. 更新数据
      this.onCostInvoiceTableChange(invoiceNo, 'details', value)

      // 2. 统计金额
      const invoiceAmount = value.reduce((prev: any, cur: any) => prev.plus(new Decimal(cur.invoiceAmount || 0)), new Decimal(0))
      const taxAmount = value.reduce((prev: any, cur: any) => prev.plus(new Decimal(cur.taxAmount || 0)), new Decimal(0))
      this.onCostInvoiceTableChange(invoiceNo, 'invoiceAmount', invoiceAmount)
      this.onCostInvoiceTableChange(invoiceNo, 'taxAmount', taxAmount)

      // 3. 重新构建映射和更新剩余金额
      this.buildInvoiceUsageMapping()
      this.updateInvoiceRemainAmounts()
    },

    /**
     * @description 上传前校验逻辑
     */
    valBeforeUpload() {
      const waitValData = [...this.selectedPaidData]
      // 错误汇总
      const errors = [] as string[]
      waitValData.forEach((item: CostInvoiceType) => {
        const { details } = item
        details && details.forEach((detail: InvoiceDetailType) => {
          const { id, realInvoiceNo, invoiceAmount, taxAmount } = detail
          if (id) return
          let errMsg = ''
          if (!realInvoiceNo) {
            errMsg = '发票号不能为空'
            detail.realInvoiceNoErrMsg = errMsg
            errors.push(errMsg)
            errMsg = ''
          }
          // // invoiceAmount 金额
          // const linkInvoice = this.invoiceList.find((invoice: InvoiceDetail) => invoice.realInvoiceNo === realInvoiceNo)
          // 使用新的校验方法
          if (realInvoiceNo) {
            // 找到当前明细在父记录中的索引
            const detailIndex = item.details.findIndex(d => d === detail)
            errMsg = this.rules.invoiceAmount(invoiceAmount, realInvoiceNo, item.invoiceNo, detailIndex)
            if (errMsg) {
              detail.invoiceAmountErrMsg = errMsg
              errors.push(errMsg)
              errMsg = ''
            }
            // taxAmount 税额
            // 使用新的校验方法
            errMsg = this.rules.taxAmount(taxAmount, realInvoiceNo, item.invoiceNo, detailIndex)
            if (errMsg) {
              detail.taxAmountErrMsg = errMsg
              errors.push(errMsg)
              errMsg = ''
            }
          }
        })
      })
      this.$nextTick(() => {
        this.selectedPaidData = waitValData
      })
      return errors
    },

    /** 上传发票 */
    submitUpload() {
      // 提交前重新计算确保数据最新
      this.updateInvoiceRemainAmounts()

      this.submitIng = true
      // 校验,错误汇总
      const errors = this.valBeforeUpload()
      if (errors.length > 0) {
        this.submitIng = false
        this.$message.error('请检查发票信息')
      } else {
        // 调用接口
        const params: UploadInvoiceReq = {
          invoices: this.uploadInvoiceParams as CostLinkInvoiceType[]
        }
        uploadInvoice(params).then(res => {
          this.$message.success('上传成功')
          this.showUploadModal = false
          // 运行低代码列表查询接口
          this.costInvoiceManage.runQuery('queryList')
        }, res => {
          const resData = res.data
          this.$message.error(resData.message || '上传失败')
        }).finally(() => {
          this.submitIng = false
        })
      }
    },

    /** 重置上传 */
    cancleUpload() {
      this.showUploadModal = false
    },

    /** 关闭预览 */
    handleCancel() {
      this.previewVisible = false
    },

    /** 枚举映射 */
    valueToLabel(value: any, enumKey: string) {
      const enums = JSON.parse(localStorage.getItem(enumKey) || '')
      return getLabelFromEnum(value, enums, ['name', 'code'])
    },
    /**
     * @description 重置并初始化发票状态（统一入口方法）
     */
    resetAndInitInvoiceState() {
      // 1. 重置发票到原始状态
      this.resetInvoiceToOriginalState()

      // 2. 清空发票使用映射
      this.invoiceUsageMap = {}

      // 3. 基于当前选中数据重新构建映射
      this.buildInvoiceUsageMapping()

      // 4. 更新发票剩余金额
      this.updateInvoiceRemainAmounts()
    },

    /**
     * @description 重置发票到原始状态
     */
    resetInvoiceToOriginalState() {
      this.invoiceList.forEach((invoice: any) => {
        if (invoice.defaultItem) {
          // 恢复到数据库原始状态
          invoice.remainInvoiceAmount = invoice.defaultItem.remainInvoiceAmount
          invoice.remainTaxAmount = invoice.defaultItem.remainTaxAmount
        } else {
          // 如果没有默认值，基于数据库数据计算
          invoice.remainInvoiceAmount = Number(new Decimal(invoice.realInvoiceAmount || 0).sub(new Decimal(invoice.usedInvoiceAmount || 0)).valueOf())
          invoice.remainTaxAmount = Number(new Decimal(invoice.realTaxAmount || 0).sub(new Decimal(invoice.usedTaxAmount || 0)).valueOf())
        }
      })
    },

    /**
     * @description 构建发票使用映射（只计算新增/编辑的记录，避免重复计算数据库已存在的记录）
     */
    buildInvoiceUsageMapping() {
      // 重置映射
      this.invoiceUsageMap = {}

      // 重新构建 - 只计算没有id的记录（新增/编辑的记录）
      this.selectedPaidData.forEach((payment: CostInvoiceType) => {
        payment.details.forEach((detail: InvoiceDetailType, detailIndex: number) => {
          // 只处理新增/编辑的记录（没有id的记录）
          // 数据库中已存在的记录（有id的）其使用量已经反映在invoice.usedInvoiceAmount中了
          if (!detail.realInvoiceNo || detail.id) return

          if (!this.invoiceUsageMap[detail.realInvoiceNo]) {
            this.invoiceUsageMap[detail.realInvoiceNo] = []
          }

          this.invoiceUsageMap[detail.realInvoiceNo].push({
            invoiceNo: payment.invoiceNo,
            detailIndex,
            invoiceAmount: Number(detail.invoiceAmount || 0),
            taxAmount: Number(detail.taxAmount || 0)
          })
        })
      })
    },

    /**
     * @description 更新发票剩余金额（基于使用映射）
     */
    updateInvoiceRemainAmounts() {
      this.invoiceList.forEach((invoice: any) => {
        const usageRecords = this.invoiceUsageMap[invoice.realInvoiceNo] || []
        let currentUsedInvoiceAmount = 0
        let currentUsedTaxAmount = 0

        // 计算当前编辑中的使用金额
        usageRecords.forEach(usage => {
          currentUsedInvoiceAmount = Number(new Decimal(currentUsedInvoiceAmount).add(new Decimal(usage.invoiceAmount || 0)).valueOf())
          currentUsedTaxAmount = Number(new Decimal(currentUsedTaxAmount).add(new Decimal(usage.taxAmount || 0)).valueOf())
        })

        // 计算剩余金额：数据库剩余 - 当前编辑使用
        const dbRemainInvoiceAmount = Number(new Decimal(invoice.realInvoiceAmount || 0).sub(new Decimal(invoice.usedInvoiceAmount || 0)).valueOf())
        const dbRemainTaxAmount = Number(new Decimal(invoice.realTaxAmount || 0).sub(new Decimal(invoice.usedTaxAmount || 0)).valueOf())

        invoice.remainInvoiceAmount = Number(new Decimal(dbRemainInvoiceAmount).sub(new Decimal(currentUsedInvoiceAmount)).valueOf())
        invoice.remainTaxAmount = Number(new Decimal(dbRemainTaxAmount).sub(new Decimal(currentUsedTaxAmount)).valueOf())

        // 确保不为负数
        invoice.remainInvoiceAmount = Math.max(0, invoice.remainInvoiceAmount)
        invoice.remainTaxAmount = Math.max(0, invoice.remainTaxAmount)
      })
    },

    /**
     * @description 处理子表变化（简化版本）
     */
    handleCalculateUsage() {
      this.updateInvoiceRemainAmounts()
    },
    /**
     * @description 更新发票使用映射中的特定记录
     * @param realInvoiceNo 发票号
     * @param invoiceNo 付款记录号
     * @param detailIndex 明细索引
     * @param invoiceAmount 发票金额
     * @param taxAmount 税额
     */
    updateInvoiceUsageMap(realInvoiceNo: string, invoiceNo: string, detailIndex: number, invoiceAmount: number, taxAmount: number) {
      if (!realInvoiceNo) return

      if (!this.invoiceUsageMap[realInvoiceNo]) {
        this.invoiceUsageMap[realInvoiceNo] = []
      }

      // 查找是否已存在该记录
      const existingIndex = this.invoiceUsageMap[realInvoiceNo].findIndex(
        usage => usage.invoiceNo === invoiceNo && usage.detailIndex === detailIndex
      )

      if (existingIndex > -1) {
        // 更新现有记录
        this.invoiceUsageMap[realInvoiceNo][existingIndex] = {
          invoiceNo,
          detailIndex,
          invoiceAmount,
          taxAmount
        }
      } else {
        // 添加新记录
        this.invoiceUsageMap[realInvoiceNo].push({
          invoiceNo,
          detailIndex,
          invoiceAmount,
          taxAmount
        })
      }
    },
    /**
     * @description 从发票使用映射中移除记录
     * @param realInvoiceNo 发票号
     * @param invoiceNo 付款记录号
     * @param detailIndex 明细索引
     */
    removeFromInvoiceUsageMap(realInvoiceNo: string, invoiceNo: string, detailIndex: number) {
      if (!realInvoiceNo || !this.invoiceUsageMap[realInvoiceNo]) return

      this.invoiceUsageMap[realInvoiceNo] = this.invoiceUsageMap[realInvoiceNo].filter(
        usage => !(usage.invoiceNo === invoiceNo && usage.detailIndex === detailIndex)
      )

      // 如果该发票没有任何使用记录，删除该键
      if (this.invoiceUsageMap[realInvoiceNo].length === 0) {
        delete this.invoiceUsageMap[realInvoiceNo]
      }
    },
    /**
     * @description 清理指定发票的所有使用映射记录
     * @param realInvoiceNo 发票号
     */
    cleanInvoiceUsageMap(realInvoiceNo: string) {
      if (!realInvoiceNo) return
      if (this.invoiceUsageMap[realInvoiceNo]) {
        delete this.invoiceUsageMap[realInvoiceNo]
      }
    },

    /**
     * @description 计算发票剩余金额（排除当前正在编辑的记录）
     * @param realInvoiceNo 发票号
     * @param currentInvoiceNo 当前编辑的付款记录号（可选）
     * @param currentDetailIndex 当前编辑的明细索引（可选）
     */
    calculateInvoiceRemainAmount(realInvoiceNo: string, currentInvoiceNo?: string, currentDetailIndex?: number): number {
      const invoice = this.invoiceList.find(inv => inv.realInvoiceNo === realInvoiceNo)
      if (!invoice) return 0

      const usageRecords = this.invoiceUsageMap[realInvoiceNo] || []
      let totalUsed = 0

      usageRecords.forEach(usage => {
        // 排除当前正在编辑的记录
        if (currentInvoiceNo && currentDetailIndex !== undefined &&
            usage.invoiceNo === currentInvoiceNo && usage.detailIndex === currentDetailIndex) {
          return
        }
        totalUsed = Number(new Decimal(totalUsed).add(new Decimal(usage.invoiceAmount)).valueOf())
      })

      return Number(new Decimal(invoice.realInvoiceAmount).sub(new Decimal(invoice.usedInvoiceAmount)).sub(new Decimal(totalUsed)).valueOf())
    },
    /**
     * @description 计算发票剩余税额（排除当前正在编辑的记录）
     * @param realInvoiceNo 发票号
     * @param currentInvoiceNo 当前编辑的付款记录号（可选）
     * @param currentDetailIndex 当前编辑的明细索引（可选）
     */
    calculateInvoiceRemainTaxAmount(realInvoiceNo: string, currentInvoiceNo?: string, currentDetailIndex?: number): number {
      const invoice = this.invoiceList.find(inv => inv.realInvoiceNo === realInvoiceNo)
      if (!invoice) return 0

      const usageRecords = this.invoiceUsageMap[realInvoiceNo] || []
      let totalUsed = 0

      usageRecords.forEach(usage => {
        // 排除当前正在编辑的记录
        if (currentInvoiceNo && currentDetailIndex !== undefined &&
            usage.invoiceNo === currentInvoiceNo && usage.detailIndex === currentDetailIndex) {
          return
        }
        totalUsed = Number(new Decimal(totalUsed).add(new Decimal(usage.taxAmount)).valueOf())
      })

      return Number(new Decimal(invoice.realTaxAmount).sub(new Decimal(invoice.usedTaxAmount)).sub(new Decimal(totalUsed)).valueOf())
    },
    /**
     * @description 校验发票金额（供子组件调用）
     * @param value 输入的金额
     * @param realInvoiceNo 发票号
     * @param currentInvoiceNo 当前付款记录号
     * @param currentDetailIndex 当前明细索引
     */
    validateInvoiceAmount(value: number, realInvoiceNo: string, currentInvoiceNo: string, currentDetailIndex: number): string {
      return this.rules.invoiceAmount(value, realInvoiceNo, currentInvoiceNo, currentDetailIndex)
    },
    /**
     * @description 校验发票税额（供子组件调用）
     * @param value 输入的税额
     * @param realInvoiceNo 发票号
     * @param currentInvoiceNo 当前付款记录号
     * @param currentDetailIndex 当前明细索引
     */
    validateTaxAmount(value: number, realInvoiceNo: string, currentInvoiceNo: string, currentDetailIndex: number): string {
      return this.rules.taxAmount(value, realInvoiceNo, currentInvoiceNo, currentDetailIndex)
    },

    /**
     * @description 计算总的已使用发票金额（排除当前编辑记录）
     */
    calculateTotalUsedInvoiceAmount(realInvoiceNo: string, currentInvoiceNo?: string, currentDetailIndex?: number): number {
      const usageRecords = this.invoiceUsageMap[realInvoiceNo] || []
      let totalUsed = 0

      usageRecords.forEach(usage => {
        // 排除当前正在编辑的记录
        if (currentInvoiceNo && currentDetailIndex !== undefined &&
            usage.invoiceNo === currentInvoiceNo && usage.detailIndex === currentDetailIndex) {
          return
        }
        totalUsed = Number(new Decimal(totalUsed).add(new Decimal(usage.invoiceAmount)).valueOf())
      })

      return totalUsed
    },

    /**
     * @description 计算总的已使用税额（排除当前编辑记录）
     */
    calculateTotalUsedTaxAmount(realInvoiceNo: string, currentInvoiceNo?: string, currentDetailIndex?: number): number {
      const usageRecords = this.invoiceUsageMap[realInvoiceNo] || []
      let totalUsed = 0

      usageRecords.forEach(usage => {
        // 排除当前正在编辑的记录
        if (currentInvoiceNo && currentDetailIndex !== undefined &&
            usage.invoiceNo === currentInvoiceNo && usage.detailIndex === currentDetailIndex) {
          return
        }
        totalUsed = Number(new Decimal(totalUsed).add(new Decimal(usage.taxAmount)).valueOf())
      })

      return totalUsed
    }
  }
})
</script>

<style lang="less" scoped>

::v-deep .ant-descriptions-row > th,::v-deep .ant-descriptions-row > td {
  padding-bottom: 0;
}

.drawer-footer{
  position: absolute;
  right: 0px;
  bottom: 0px;
  width: 100%;
  border-top: 1px solid rgb(233, 233, 233);
  padding: 10px 16px;
  background: rgb(255, 255, 255);
  text-align: right;
  z-index: 1;
}
::v-deep .ant-upload-list-item-actions{
  a {
    pointer-events: initial !important;
    opacity: 1 !important;
  }
}
::v-deep .ant-drawer-body{
  padding-bottom: 54px;
}
</style>
