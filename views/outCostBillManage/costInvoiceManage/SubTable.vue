<template>
  <div class="sub-table-container">
    <a-table
      :pagination="false"
      :dataSource="linkInvoiceList"
      :columns="columns"
      size="small"
      :bordered="true"
      :rowKey="'invoiceNo'"
    >
      <!-- 真实发票编号 -->
      <template slot="realInvoiceNo" slot-scope="text, record, index">
        <!-- <editable-cell :index="index" :record="record" :options="curOptions" @change="onRealInvoiceNoChange" /> -->
        <div v-if="!record.id">
          <div style="width: 100%">
            <a-select
              :options="curOptions"
              :getPopupContainer="(triggerNode) => triggerNode.parentNode"
              :value="text"
              class="invoice-select"
              :class="record['realInvoiceNoErrMsg']?'err':''"
              @change="(e)=> onRealInvoiceNoChange(index, e)"/>
            <div v-if="record['realInvoiceNo'+'ErrMsg']" class="err-msg">
              {{ record.realInvoiceNoErrMsg }}
            </div>
          </div>
        </div>
        <div v-else class="editable-cell-text-wrapper">
          {{ text || ' ' }}
        </div>
      </template>
      <!-- 实际输入发票金额、税额 -->
      <template v-for="col in ['taxAmount', 'invoiceAmount']" :slot="col" slot-scope="text, record, index">
        <div :key="col">
          <div v-if="!record.id" style="margin: -5px 0">
            <a-input-number
              style="width: 100%"
              :value="text"
              :precision="2"
              :formatter="formatterHandler"
              :parser="amountParser"
              :class="record[col+'ErrMsg'] ? 'err' : ''"
              @change="(e) => handleChange(index, col, e)"
              @blur="handleBlur"
            />
            <div v-if="record[col+'ErrMsg']" class="err-msg">{{ record[col+'ErrMsg'] }}</div>
            <div v-if="col === 'taxAmount'" class="remain-info">
              {{
                `剩余税额：${ remainTaxAmount(record) }`
              }}
            </div>
            <div v-else class="remain-info">
              {{
                `剩余发票金额：${ remainAmount(record) }`
              }}
            </div>
          </div>
          <div v-else>
            {{ toThousands( Number( text || 0 ).toFixed(2) ) }}
          </div>
        </div>
      </template>
      <template slot="realInvoiceAmount" slot-scope="text">
        <span>{{ toThousands( Number( text || 0 ).toFixed(2) ) }}</span>
      </template>
      <template slot="action" slot-scope="text, record, index">
        <a-popconfirm v-if="!record.id" title="确认删除吗?" @confirm="() => onDelete(index)">
          <a href="javascript:;">删除</a>
        </a-popconfirm>
      </template>
      <!-- 底部新增 -->
      <template slot="footer">
        <div class="sub-footer-invoice">
          <a-button type="dashed" :block="true" icon="plus" style="opacity: 0.8" @click="add">新增</a-button>
        </div>
      </template>
    </a-table>
  </div>
</template>

<script lang="ts">
import { InvoiceDetailType } from '_/types/invoiceManage'
import { InvoiceColumns } from './define/colums'
import { defineComponent } from '@vue/composition-api'
import { InvoiceDetail } from '_/api/types/costInvoiceManage'
import { toThousands } from '_/utils'
import Decimal from 'decimal.js'

export default defineComponent({
  props: {
    invoiceMap: {
      type: Object,
      default: () => ({})
    },
    /** 全局已使用的发票映射 */
    invoiceUsageMap: {
      type: Object,
      default: () => ({})
    },
    /** 付款记录对应的发票 */
    parentRecord: {
      type: Object,
      required: true
    },
    parentIndex: {
      type: Number,
      required: true
    },
    options: {
      type: Array,
      default: () => []
    },
    /** 父组件的发票金额校验方法 */
    validateInvoiceAmount: {
      type: Function,
      required: true
    },
    /** 父组件的发票税额校验方法 */
    validateTaxAmount: {
      type: Function,
      required: true
    },
    /** 父组件的剩余金额计算方法 */
    calculateInvoiceRemainAmount: {
      type: Function,
      required: true
    },
    /** 父组件的剩余税额计算方法 */
    calculateInvoiceRemainTaxAmount: {
      type: Function,
      required: true
    }
  },
  data() {
    // 付款记录对应的发票列表
    const { details = [] as InvoiceDetailType[] } = JSON.parse(JSON.stringify(this.parentRecord))
    const _details = details || []
    const _options = JSON.parse(JSON.stringify(this.options)) || []

    return {
      /** 千分位转换方法 */
      toThousands,
      /** 发票表格列头 */
      columns: InvoiceColumns,
      /** 关联的发票集合 */
      linkInvoiceList: _details as InvoiceDetailType[],
      curOptions: _options
    }
  },
  watch: {
    /** 父级如果更新，要实时更新下拉选 */
    options: {
      handler(val) {
        this.updateOptions()
      },
      deep: true
    },
    /** 监听全局发票使用映射的变化 */
    invoiceUsageMap: {
      handler() {
        this.updateOptions()
      },
      deep: true
    },
    /** 附表格记录 */
    parentRecord: {
      handler(val) {
        const { details = [] as InvoiceDetailType[] } = JSON.parse(JSON.stringify(val))

        this.linkInvoiceList = details
        this.updateOptions()
      },
      deep: true
    }
  },
  methods: {
    /**
     *剩余发票金额
     */
    remainAmount(record: any) {
      if (!record.realInvoiceNo) return '0.00'
      // 直接从父组件更新的 invoiceMap 获取最新剩余金额
      const invoice = this.invoiceMap[record.realInvoiceNo]

      if (!invoice) return '0.00'

      // 直接使用父组件计算好的剩余金额，不需要再减去已使用金额
      return toThousands(Number(invoice.remainInvoiceAmount || 0).toFixed(2))
    },
    /**
     *剩余税额
     */
    remainTaxAmount(record: any) {
      if (!record.realInvoiceNo) return '0.00'
      // 直接从父组件更新的 invoiceMap 获取最新剩余税额
      const invoice = this.invoiceMap[record.realInvoiceNo]

      if (!invoice) return '0.00'

      // 直接使用父组件计算好的剩余税额，不需要再减去已使用税额
      return toThousands(Number(invoice.remainTaxAmount || 0).toFixed(2))
    },

    // 计算剩余金额（排除当前编辑记录）
    calculateRemainAmountForCurrentRecord(realInvoiceNo: string, currentIndex: number): number {
      const invoice = this.invoiceMap[realInvoiceNo]
      if (!invoice) return 0

      let remain = (invoice.realInvoiceAmount || 0) - (invoice.usedInvoiceAmount || 0)
      this.linkInvoiceList.forEach((detail, index) => {
        if (index !== currentIndex && detail.realInvoiceNo === realInvoiceNo && detail.invoiceAmount) {
          const amount = Number(detail.invoiceAmount) || 0
          if (amount > 0) {
            remain -= amount
          }
        }
      })
      console.log('计算剩余金额:', {
        realInvoiceNo,
        currentIndex,
        realInvoiceAmount: invoice.realInvoiceAmount,
        usedInvoiceAmount: invoice.usedInvoiceAmount,
        remain: Math.max(0, remain)
      })
      return Math.max(0, remain)
    },

    // 计算剩余税额（排除当前编辑记录）
    calculateRemainTaxAmountForCurrentRecord(realInvoiceNo: string, currentIndex: number): number {
      const invoice = this.invoiceMap[realInvoiceNo]
      if (!invoice) return 0

      let remain = (invoice.realTaxAmount || 0) - (invoice.usedTaxAmount || 0)
      this.linkInvoiceList.forEach((detail, index) => {
        if (index !== currentIndex && detail.realInvoiceNo === realInvoiceNo && detail.taxAmount) {
          const amount = Number(detail.taxAmount) || 0
          if (amount > 0) {
            remain -= amount
          }
        }
      })
      console.log('计算剩余税额:', {
        realInvoiceNo,
        currentIndex,
        realTaxAmount: invoice.realTaxAmount,
        usedTaxAmount: invoice.usedTaxAmount,
        remain: Math.max(0, remain)
      })
      return Math.max(0, remain)
    },
    /**
     * @description 更新发票下拉选（最终修复版本）
     */
    updateOptions() {
      if (!this.curOptions) return

      // 获取当前付款记录（parentRecord）已选择的发票号
      const currentRowSelectedInvoices = this.parentRecord.details
        ? this.parentRecord.details.map((item: InvoiceDetailType) => item.realInvoiceNo).filter(Boolean) : []

      this.$nextTick(() => {
        this.curOptions = this.options.filter((item: any) => {
          const invoiceNo = item.value || item.realInvoiceNo

          // 1. 基于数据库状态过滤完全使用的发票
          const dbRemainAmount = Number(new Decimal(item.realInvoiceAmount || 0).sub(new Decimal(item.usedInvoiceAmount || 0)).valueOf())
          const dbRemainTax = Number(new Decimal(item.realTaxAmount || 0).sub(new Decimal(item.usedTaxAmount || 0)).valueOf())
          const hasDbRemaining = dbRemainAmount > 0 || dbRemainTax > 0

          // 2. 检查当前付款记录是否已经选择了这个发票（避免同一付款记录重复选择同一发票）
          const countInCurrentRow = currentRowSelectedInvoices.filter(selected => selected === invoiceNo).length
          const notDuplicateInCurrentRow = countInCurrentRow === 0

          return hasDbRemaining && notDuplicateInCurrentRow
        })
      })
    },
    // 金额去掉空格
    amountParser (value: any) {
      return value.replace(/\$\s?|(,*)/g, '')
    },
    formatterHandler (value: any) {
      return `${value}`.replace(/\B(?=(\d{3})+(?!\d))/g, ',')
    },
    /**
     * @description 修改表格数据公共方法
     * @param index 修改的哪一行
     * @param column 修改的哪一列
     * @param value 修改的值
     */
    handleChange(index: number, column: string, value: any, validate = true) {
      const newData = [...this.linkInvoiceList]
      const target = newData[index]
      if (target) {
        target[column] = value

        // 当用户输入实收发票金额(含税)时，自动计算实收税额
        if (column === 'invoiceAmount' && target.realInvoiceNo) {
          const invoiceAmount = Number(value) || 0
          const invoice = this.invoiceMap[target.realInvoiceNo]

          if (invoiceAmount <= 0 || !invoice) {
            target.taxAmount = 0
          } else {
            // 先更新当前数据，再计算剩余金额（这样计算会更准确）
            this.linkInvoiceList = newData

            // 使用父组件的方法计算真实的剩余金额，排除当前正在编辑的记录
            const remainAmount = this.calculateInvoiceRemainAmount(target.realInvoiceNo, this.parentRecord.invoiceNo, index) || 0
            const remainTaxAmount = this.calculateInvoiceRemainTaxAmount(target.realInvoiceNo, this.parentRecord.invoiceNo, index) || 0

            // 根据发票税额计算规则：
            // 如果输入金额 < 剩余发票金额，则税额 = 输入金额/(1+0.06)*0.06
            // 如果输入金额 >= 剩余发票金额，则税额 = 发票税额 - 已使用税额
            const invoiceAmountDecimal = new Decimal(invoiceAmount)
            const remainAmountDecimal = new Decimal(remainAmount)

            let calculatedTaxAmount = 0
            if (invoiceAmountDecimal.lt(remainAmountDecimal)) {
              // 使用公式计算：输入金额/(1+0.06)*0.06
              calculatedTaxAmount = Math.max(0, Number(invoiceAmountDecimal.div(1.06).mul(0.06).toFixed(2)))
            } else {
              // 使用剩余税额
              calculatedTaxAmount = Math.max(0, remainTaxAmount)
            }

            target.taxAmount = calculatedTaxAmount
          }
        }

        this.linkInvoiceList = newData

        // 先通知父组件更新数据，这样父组件会重新计算发票使用映射

        this.$emit('tableChange', this.parentRecord.invoiceNo, newData)

        // 只在特定情况下进行校验，避免输入过程中的误报
        if (validate && target.realInvoiceNo) {
          // 检查是否是完整的数字输入（避免在输入小数点时校验）
          const isCompleteNumber = (val: any) => {
            const str = String(val)
            // 如果以小数点结尾，认为还在输入中
            if (str.endsWith('.')) return false
            // 如果是有效数字，认为输入完成
            return !isNaN(Number(val)) && val !== ''
          }

          // 延迟校验，确保父组件的发票使用映射已经更新
          this.$nextTick(() => {
            if (column === 'invoiceAmount' && isCompleteNumber(value)) {
              // 校验发票金额
              target[column + 'ErrMsg'] = this.validateInvoiceAmount(value, target.realInvoiceNo, this.parentRecord.invoiceNo, index)

              // 如果自动计算了税额，校验税额
              if (target.taxAmount && target.taxAmount !== undefined) {
                target['taxAmountErrMsg'] = this.validateTaxAmount(target.taxAmount, target.realInvoiceNo, this.parentRecord.invoiceNo, index)
              }
            } else if (column === 'taxAmount' && isCompleteNumber(value)) {
              // 校验税额
              target[column + 'ErrMsg'] = this.validateTaxAmount(value, target.realInvoiceNo, this.parentRecord.invoiceNo, index)
            } else if (!isCompleteNumber(value)) {
              // 如果是不完整的输入，清除错误信息
              target[column + 'ErrMsg'] = ''
              if (column === 'invoiceAmount') {
                target['taxAmountErrMsg'] = ''
              }
            }

            // 强制更新视图以显示所有错误信息
            this.$forceUpdate()
          })
        }
      }
    },
    handleBlur() {
      // 触发父组件重新计算发票使用总量
      this.$emit('calculateUsage')
      // 注意：这里不再重复发送 tableChange 事件，因为 handleChange 中已经发送过了
      // 避免重复计算导致的数据错误
      // this.$emit('tableChange', this.parentRecord.invoiceNo, [...this.linkInvoiceList])
    },
    /**
     * @description 更新真实发票数据
     * @param index 行索引
     * @param value 修改的值
     */
    onRealInvoiceNoChange(index: number, value: string) {
      this.handleChange(index, 'realInvoiceNo', value)
      // 真实发票金额
      const selectInvoice: InvoiceDetail = ((this.options as InvoiceDetail[]).find((item: InvoiceDetail) => item.realInvoiceNo === value)) as InvoiceDetail
      // 将发票信息更新到关联的发票记录上
      this.handleChange(index, 'originInvoiceInfo', selectInvoice)
      this.handleChange(index, 'realInvoiceAmount', selectInvoice.realInvoiceAmount)
      this.handleChange(index, 'realTaxAmount', selectInvoice.realTaxAmount)
      this.handleChange(index, 'usedInvoiceAmount', selectInvoice.usedInvoiceAmount)
      this.handleChange(index, 'usedTaxAmount', selectInvoice.usedTaxAmount)
      // 重置发票金额、税额
      this.handleChange(index, 'realInvoiceNoErrMsg', '', false)
      this.handleChange(index, 'invoiceAmount', '', false)
      this.handleChange(index, 'invoiceAmountErrMsg', '', false)
      this.handleChange(index, 'taxAmount', '', false)
      this.handleChange(index, 'taxAmountErrMsg', '', false)
    },

    /**
     * @description 删除行数据
     * @param selectRow 删除的行
     */
    onDelete(index: number) {
      // this.$emit('delete', selectRow)
      this.linkInvoiceList.splice(index, 1)
      const newData = [...this.linkInvoiceList]
      this.$emit('tableChange', this.parentRecord.invoiceNo, newData)
    },

    /** 新增 */
    add() {
      const len = this.linkInvoiceList && (this.linkInvoiceList.length || 0)
      const _len = this.linkInvoiceList && this.linkInvoiceList.filter((item: InvoiceDetailType) => item.realInvoiceNo).length || 0
      // 如果有未填写的，不允许新增
      len === _len && this.linkInvoiceList.push({
        invoiceNo: 'fe_' + new Date().getTime(),
        invoiceAmountErrMsg: '',
        taxAmountErrMsg: '',
        realInvoiceNoErrMsg: '',
      } as unknown as InvoiceDetailType)
    }
  }
})
</script>

<style lang="less" scoped>
.err{
  border: 1px solid red;
}
.err-msg{
  font-size: 12px;
  text-align: left;
  color: red;
}
.invoice-select{
  max-width: 220px;
  min-width: 220px
}
.remain-info{
  font-size: 12px;
  text-align: left;
}
</style>
