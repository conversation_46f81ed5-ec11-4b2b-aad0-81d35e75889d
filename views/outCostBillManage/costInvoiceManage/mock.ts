import { CostInvoiceType } from '_/types/invoiceManage'
export const selectData: CostInvoiceType[] = [
  {
    id: 123,
    invoiceNo: 'invoiceNo123',
    orderNo: 'a1b2c3d4-e5f6-7890-abcd-ef1234567890',
    status: 'Paid',
    bankCode: 'ABC',
    paymentMode: 'Credit Card',
    institutionType: 'Tech Corp',
    paymentPeriod: '2024-05-01 00:00:00',
    paymentTime: '2024-05-01 00:00:00',
    amount: 1000.0,
    receivableInvoiceAmount: 200.01,
    realInvoiceAmount: 50.0,
    invoiceAmount: ***********.0,
    taxAmount: 50.0,
    remark: 'Paid in full',
    createTime: '2021-12-01T00:00:00.000Z',
    updateTime: '2023-10-01T00:00:00.000Z',
    cardType: 'FOREX_PURCHASE',
    businessType: 'AGREEMENT_PAY',
    details: [
      {
        id: 1,
        invoiceNo: '111',
        superInvoiceNo: '111-1',
        realInvoiceNo: '111-1-1',
        realInvoiceAmount: 123,
        realTaxAmount: 223123.123,
        taxAmount: 223123.123,
        invoiceAmount: 123,
        invoiceInfo: 'string',
        usedTaxAmount: 100,
        usedInvoiceAmount: 100,
        remark: '111-r',
        createTime: '2024-05-01 00:00:00',
        updateTime: '2024-05-01 00:00:00',
        invoiceAmountErrMsg: '',
        taxAmountErrMsg: ''
      },
      {
        id: 2,
        invoiceNo: '222',
        superInvoiceNo: '222-2',
        realInvoiceNo: '222-2-2',
        realInvoiceAmount: *********.111123,
        realTaxAmount: *********.123123,
        taxAmount: 456,
        invoiceAmount: 456,
        usedTaxAmount: 200,
        usedInvoiceAmount: 200,
        invoiceInfo: '123123',
        remark: '222-r',
        createTime: '2024-05-01 00:00:00',
        updateTime: '2024-05-01 00:00:00',
        invoiceAmountErrMsg: '',
        taxAmountErrMsg: ''
      },
      {
        invoiceNo: '444',
        superInvoiceNo: '222-2',
        realInvoiceNo: '222-2-2',
        realInvoiceAmount: *********.111123,
        realTaxAmount: *********.123123,
        taxAmount: 223123.123,
        taxAmountErrMsg: '税额不一致',
        invoiceAmount: 223,
        usedTaxAmount: 221,
        usedInvoiceAmount: 221,
        invoiceInfo: '123123',
        remark: '222-r',
        createTime: '2024-05-01 00:00:00',
        updateTime: '2024-05-01 00:00:00',
        invoiceAmountErrMsg: ''
      }
    ]
  },
  {
    id: 12345,
    invoiceNo: 'invoiceNo12345',
    orderNo: 'a1b2c3d4-e5f6-7890-abcd-ef1234567890',
    status: 'Paid',
    bankCode: 'ABC',
    paymentMode: 'Credit Card',
    institutionType: 'Tech Corp',
    paymentPeriod: '2024-05-01 00:00:00',
    paymentTime: '2024-05-01 00:00:00',
    amount: 1000.0,
    receivableInvoiceAmount: 111100.0,
    realInvoiceAmount: 95.0,
    invoiceAmount: 12351.123,
    taxAmount: 50.0,
    remark: 'Paid in full',
    createTime: '2021-12-01T00:00:00.000Z',
    updateTime: '2023-10-01T00:00:00.000Z',
    cardType: 'jiejika2',
    businessType: 'auth2',
    details: []
  }
]
