{"name": "boss-fund-accounting", "version": "1.0.0", "description": "运营后台 - 资金账户", "main": "main.ts", "scripts": {"postinstall": "rm -rf node_modules/vue", "dev": "node bin/start.js --port=8013", "build": "node bin/build.js", "lint": "eslint --ext .js,.vue .", "prepare": "husky install"}, "author": "", "license": "ISC", "husky": {"hooks": {"pre-commit": "lint-staged"}}, "lint-staged": {"*.{js,vue}": ["eslint --fix", "git add"]}, "dependencies": {"@vue/composition-api": "1.7.2", "@yeepay/antd-materials": "2.24.17", "@yeepay/lowcode-renderer": "2.24.3", "ant-design-vue": "1.7.8", "babel-plugin-import": "^1.13.8", "decimal.js": "^10.4.3", "numeral": "^2.0.6", "radash": "^12.1.0", "sm-crypto": "^0.3.13"}, "devDependencies": {"@types/numeral": "^2.0.5", "@yeepay/server-utils": "0.0.2", "husky": "^8.0.0", "lint-staged": "^10.5.1", "rimraf": "^3.0.2", "shell-spawn": "2.0.3", "string-width": "^4.2.3"}, "resolutions": {"string-width": "^4.2.3", "strip-ansi": "^6.0.1", "is-fullwidth-code-point": "^3.0.0"}}