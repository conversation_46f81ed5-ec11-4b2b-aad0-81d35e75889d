// 调账记录
export type AdjustRecord = {
  /** 业务类型 */
  businessType: string,
  /** 调账金额 */
  amount: number | string,
  /** 调账符号 */
  symbol: '+' | '-',
  /** 是否计算 */
  calculated: 0 | 1,
  /** 备注 */
  remark?: string,
  [key: string]: any
}

// 计算过程
export type CalcProcess = {
  /** 业务类型 */
  trxType: string,
  /** 符号 */
  symbol: '+' | '-',
  /** 计算公式 */
  payAmount: number | string,
  [key: string]: any
}
