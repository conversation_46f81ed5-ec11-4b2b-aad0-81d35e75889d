
export type RulerDetailItem = {
  /** 规则明细id */
  id?: number,
  /** 规则主表id */
  ruleId?: number,
  /** 规则符号 */
  symbol: '+' | '-' | '',
  /** 报表x */
  reportTable: string,
  /** x报表字段 */
  reportField: string,
  /** 规则明细左右位置 */
  checkPosition: 'L' | 'R',
  [ key: string ]: any
}

export type ReportRulerItem = {
  /** 报备规则唯一标识 */
  id: number,
  /** 清算机构 */
  clearOrgBank: string,
  /** 状态 */
  status: string,
  /** 勾稽关系 */
  checkMethod: '=',
  /** 规则左 */
  detailVoL: RulerDetailItem [],
  /** 规则右 */
  detailVoR: RulerDetailItem [],
  /** 备注 */
  remark: string
  checkInfo: string,
  [ key: string ]: any
}
