/**
 * @description: 对账接口配置
 */

// 规则明细
export type RuleDetaillVo = {
    ruleType?: string; // 规则类型
    datasourceConfig: string; // 数据源配置
    errorType: string; // 差错类型
    sqlStr: string; // SQL语句
    outputColumn: string; // 输出映射关系
    outputTableName: string; // 输出数据表名
    [key: string]: any;
}

// 规则
export type ConfigRule = {
    configCode: string; // 规则编码
    configRuleName: string; // 规则组名称
    notifyWay: string; // 单边通知方式
    notifyWayDesc: string; // 单边通知方式描述
    appName: string; // 业务系统名称
    fileJobId: string; // 文件解析作业编码
    isEdit: any; // 是否可编辑（暂未明确使用）
    ruleDetaillVoList: RuleDetaillVo[]; // 规则明细
    // 以下为新增字段
    [key: string]: any;
}
