export type InvoiceInfoType = {
  url: string
  taxNo: string
  fileName: string
  fileType: string
  invoiceName: string
  invoiceNumber: string
}
export type InvoiceDetailType = {
  id?: number
  invoiceNo: string
  superInvoiceNo: string
  realInvoiceNo: string
  invoiceAmountErrMsg: string
  realInvoiceAmount: number
  realTaxAmount: number
  taxAmount: number
  taxAmountErrMsg: string
  invoiceAmount: number
  invoiceAmountErrMsg: string
  usedInvoiceAmount: number
  usedTaxAmount: number
  invoiceInfo: string
  remark: string
  createTime: string
  updateTime: string
  /** 索引签名 */
  [key: string]: any; // 使用索引签名，定义任意属性
}

/**
 * @interface CostInvoiceType
 *  成本发票类型
 *  ps: 分页查询分页列表
 */
export type CostInvoiceType = {
  /** 发票ID */
  id: number
  /** 关联发票主键 */
  invoiceNo: string
  /** 订单号 */
  orderNo: string
  /** 状态 */
  status: string
  /** 银行代码 */
  bankCode: string
  /** 业务类型 */
  businessType: string
  /** 卡类型 */
  cardType: string
  /** 支付方式 */
  paymentMode: string
  /** 机构类型 */
  institutionType: string
  /** 支付周期 */
  paymentPeriod: string | null
  /** 支付时间 */
  paymentTime: string
  /** 金额 */
  amount: number
  /** 应收发票金额 */
  receivableInvoiceAmount: number
  /** 实际发票金额 */
  invoiceAmount: number
  /** 税额 */
  taxAmount: number
  /** 关联发票信息列表 */
  details: InvoiceDetailType[]
  /** 备注 */
  remark: string
  /** 创建时间 */
  createTime: string | null
  /** 更新时间 */
  updateTime: string
  /** 索引签名 */
  [key: string]: any; // 使用索引签名，定义任意属性
}

// BindInfoType
export type BindInvoiceType = {
  invoiceNo: number
  taxAmount: number
  realInvoiceAmount: number
  receivableInvoiceAmount: number
  // 发票金额
  invoiceAmount: number
  invoiceTaxAmount: number
}
