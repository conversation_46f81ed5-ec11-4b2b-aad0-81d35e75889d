import { EnumBase } from '_/types/enums/enums'
// 计费优先级基本信息
export interface CalculatePrecedenceBaseInfo {
  id: number
  calculatePrecedence: string[]
}

// 计费元素详情
export interface CalElementDetail {
  key: string
  value: string[]
}
// 对账接口/技术服务商 结算方式
export interface CheckCodesAndItService {
  // 对账接口编号
  checkCode?: string
  // 技术服务商
  itServiceVendor?: string
  // 对账接口名称
  checkName?: string
  // 清算机构代码
  clearingOrgCode?: string
  // 支付接口代码集合
  payInterfaceCodes?: string[]
  // Oracle编码
  oracleCode?: string
  // 状态
  status?: string
  // 操作人
  operator?: string
}
// 通用计费规则
export interface CommonRule {
  id?: number
  clearingOrgCode?: string
  settlementType?: string
  ruleName?: string
}
// 计费规则项
export interface CalFeeRule {
  id?: number // id
  calFeeType: string // 计费类型
  calRuleId: number // 计算规则ID
  refundByProportion: string // 是否按比例退款
  calConfigRule: string // 计算类型(固定金额、费率)
  value: string // 配置值
  maxValue: string // 封顶
  minValue: string // 保底
  tradeType: string // 交易类型(支付、退款)
  feeType: string // 费用类型
  cardType: string // 卡类型(借记卡、贷记卡)
  amountStart?: number // 交易区间开始
  amountEnd?: number // 交易区间结束
  calFormula?: string // 计算公式
  [key: string]: any // 其他属性
}
// 通用计费规则项-展示
// 将交易和退款合并
export interface CalFeeRuleDisplay {
  id: string // id
  cardType?: string // 卡类型(借记卡、贷记卡)
  feeType?: string // 费用类型
  PAY: CalFeeRule // 支付
  REFUND: CalFeeRule // 退款
  tradeRange?: { // 交易区间
    amountStart?: number // 区间开始
    amountEnd?: number // 区间结束
  }
  [key: string]: any // 其他属性
}

// 计费规则详情-通用
export interface CalFeeRuleDetail {
  id: string // id
  clearingOrgCode: string // 清算机构代码
  settlementType: string // 结算方式
  ruleName?: string // 规则名称
  calculateType?: string // 计算类型(无阶梯、有阶梯)
  calRules: CalFeeRule[] // 计费规则列表
  feeTypes: string[] // 费用类型数组
  [key: string]: any // 其他属性
}

// -----------------------普通计费规则-----------------------
// 路由表基本信息
export interface RouteItem {
  // 计费规则ID
  id: number
  // 清算机构代码
  clearingOrgCode: string
  // 费用类型
  feeTypes: string[]
  // // 费用模式标识
  // feeTypeNum: number
  // 结算方式
  settlementType: string
  // 对账接口
  checkCode: string
  // 技术服务商
  itServiceVendor: string
  // 通用计费规则ID
  parentId: number
  // 排序优先级
  ruleOrder: number
  // 计费优先级ID
  calculatePrecedenceId: number
  // 生效时间开始
  validityStartTime: string
  // 生效时间结束
  validityEndTime: string
  // 操作人
  operator: string
  // 创建时间
  createTime: string
  // 最后修改时间
  lastModifyTime: string
  // 计费元素
  calculatePrecedenceItem: string[]
  // 计费规则详情
  calRuleDetail: CalElementDetail[]
}

// 路由列表-展示-通用
export interface RouteListDisplay {
  // 路由id
  id: number
  // 排序优先级
  ruleOrder: number
  // 计费元素项
  calculatePrecedenceItem: string[]
  // 计费元素详情
  calRuleDetail: CalElementDetail[]
  // 动态计费元素列
  [key: string]: any
  // 扩展-规则列表
  expandedRowRender: CalFeeRule[]
}

// 路由列表-公共信息-通用
export interface RouteBaseInfo {
  // 清算机构
  clearingOrgCode: string
  // 对账接口
  checkCode: string
  // 第三方服务商
  itServiceVendor: string
  // 结算方式
  settlementType: string
  // 默认计费规则
  parentId: number
  // 计费优先级id
  calculatePrecedenceId: number
  // 生效时间开始
  validityStartTime: string
  // 生效时间结束
  validityEndTime: string
  // // 费用模式标识
  // feeTypeNum: number
  // 费用类型
  feeTypes: EnumBase[]
}
// 路由子表格计费规则-展示
export interface RouteCalFeeRuleDisplay {
  // 规则ID
  id: number
  // 计费规则ID
  calRuleId: number
  // 交易类型
  tradeType: string
  // 计费类型
  calFeeType: string
  // 交易区间
  amountRange: string
  // 计费规则描述
  calRuleDesc: string
  // 保底
  minValue: string
  // 封顶
  maxValue: string
  // 生效期间
  validityTimeRange: [string, string]
}

// 计费规则详情-普通
export interface CalFeeRuleDetailNormal {
  // 工单编号
  ticketNo: string
  // 工单名称
  ticketName: string
  // 计费类型
  calculateType: string
  // 生效期间开始时间
  validityStartTime: string
  // 生效期间结束时间
  validityEndTime: string
  // 累计开始时间
  accumulationStartTime: string
  // 累计结束时间
  accumulationEndTime: string
  // 计费规则列表
  calRules: CalFeeRule[]
}

// 计费规则弹窗-表单
export interface RuleForm {
  ticketNo: string
  ticketName: string
  calculateType: string
  accumulationTime: [string, string]
  validityTime: [string, string]
}

// 计费元素组
export interface CalEleGroup {
  // 计费元素组id
  id: number
  // 计费元素项集合
  calculatePrecedences: string[],
  calculatePrecedencesDesc?: string
}
