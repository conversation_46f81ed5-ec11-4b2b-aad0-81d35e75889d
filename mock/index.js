import { isIE } from '@/utils'

// 判断环境不是 prod 是 true 时，加载 mock 服务
if (process.env.NODE_ENV !== 'production') {
  if (isIE()) {
    console.error('[yeepay-mock] ERROR: `mockjs` NOT SUPPORT `IE` PLEASE DO NOT USE IN `production` ENV.')
  }
  // 使用同步加载依赖
  // 防止 vuex 中的 GetInfo 早于 mock 运行，导致无法 mock 请求返回结果
  console.log('[yeepay-mock] mock mounting')
  const Mock = require('mockjs2')
  require('./services/demo')

  Mock.setup({
    timeout: 800 // setter delay time
  })
  console.log('[yeepay-mock] mock mounted')
}
