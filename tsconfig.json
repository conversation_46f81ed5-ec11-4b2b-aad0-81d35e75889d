{
  "extends": "../../../tsconfig",
  "compilerOptions": {
    "baseUrl": "./",
    "outDir": "dist",
    "typeRoots": [
      "./node_modules/@types/",
      "../../../node_modules/@types/",
      "../../../typings/",
      "./types/",
      "./",
      "./api/"
    ],
    "types": ["../../../node_modules/@types/node", "./vue-shims.d.ts"],
    "paths": {
      "_/*": ["./*"],
      "@/*": ["../../../src/*"],
    }
  },
  "exclude": [
    "node_modules",
    "dist"
  ]
}