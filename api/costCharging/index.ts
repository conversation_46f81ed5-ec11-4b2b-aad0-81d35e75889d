import request from '_/utils/request'
import { CalculatePrecedenceReq, CalculatePrecedenceRes, CalRuleDetailReq, CalRuleDetailRes, OperateCalRuleReq, OperateCalRuleRes, OperateRuleRouteReq, OperateRuleRouteRes, QueryCalEleGroupReq, QueryCalEleGroupRes, QueryCalRuleByRuleIdReq, QueryCalRuleByRuleIdRes, QueryCalRulesByRouteIdReq, QueryCalRulesByRouteIdRes, QueryCheckCodesAndItServiceReq, QueryCheckCodesAndItServiceRes, QueryCommonRuleListReq, QueryCommonRuleListRes, QueryRuleRouteListReq, QueryRuleRouteListRes, UpdateCommonRuleReq, UpdateCommonRuleRes } from '_/api/types/costCharging'
// 获取计费优先级
// /cost-manage-server/calculatePrecedence/query
export const queryCalculatePrecedenceApi = async (data: CalculatePrecedenceReq): Promise<CalculatePrecedenceRes> => {
  const res = await request.post('/cost-manage-server/calculatePrecedence/query', data)
  return (await res).data
}
// 通用计费规则列表
// /cost-manage-server/commonRuleRoute/queryCommonRuleList
export const queryCommonRuleListApi = async (data: QueryCommonRuleListReq): Promise<QueryCommonRuleListRes> => {
  const res = await request.post('/cost-manage-server/commonRuleRoute/queryCommonRuleList', data)
  return (await res).data
}

// 查询通用计费规则-详情
export const queryCalRuleDetailApi = async (data: CalRuleDetailReq): Promise<CalRuleDetailRes> => {
  const res = await request.get('/cost-manage-server/commonRuleRoute/queryCalRuleDetail', {
    params: data
  })
  return (await res).data
}

// 修改通用计费规则
export const updateCommonRuleApi = async (data: UpdateCommonRuleReq): Promise<UpdateCommonRuleRes> => {
  const res = await request.post('/cost-manage-server/commonRuleRoute/modifyCommonRule', data)
  return (await res).data
}

// -----------------------普通计费规则-----------------------

// 查询详情 /ruleRoute/queryRuleRouteDetail
export const queryRuleRouteListApi = async (data: QueryRuleRouteListReq): Promise<QueryRuleRouteListRes> => {
  const res = await request.post('/cost-manage-server/ruleRoute/queryRuleRouteDetail', data)
  return (await res).data
}

// 根据路由id查询计费规则列表
export const queryCalRulesByRouteIdApi = async (data: QueryCalRulesByRouteIdReq): Promise<QueryCalRulesByRouteIdRes> => {
  const res = await request.get('/cost-manage-server/ruleRoute/queryCalRuleByRouteId', {
    params: data
  })
  return (await res).data
}

// 根据计算规则id查询计费规则列表
export const queryCalRuleByRuleIdApi = async (data: QueryCalRuleByRuleIdReq): Promise<QueryCalRuleByRuleIdRes> => {
  const res = await request.get('/cost-manage-server/ruleRoute/queryCalRuleByRuleId', {
    params: data
  })
  return (await res).data
}

// 新增/修改计费优先级
export const operateRuleRouteApi = async (data: OperateRuleRouteReq): Promise<OperateRuleRouteRes> => {
  const res = await request.post('/cost-manage-server/ruleRoute/addRuleRouteList', data)
  return (await res).data
}

// 新增/修改计费规则
export const operateCalRuleApi = async (data: OperateCalRuleReq): Promise<OperateCalRuleRes> => {
  const res = await request.post('/cost-manage-server/ruleRoute/saveOrModifyCalRule', data)
  return (await res).data
}

// 获取计费元素规则组
export const queryCalEleGroupApi = async (data: QueryCalEleGroupReq): Promise<QueryCalEleGroupRes> => {
  const res = await request.post('/cost-manage-server/calculatePrecedence/query', data)
  return (await res).data
}

// 获取配置了结算方式的对账接口/技术服务商
// /cost-manage-server/payInterfaceConfig/listByClearingOrgCode
export const queryCheckCodesAndItServiceApi = async (data: QueryCheckCodesAndItServiceReq): Promise<QueryCheckCodesAndItServiceRes> => {
  const res = await request.get('/cost-manage-server/payInterfaceConfig/listByClearingOrgCode', {
    params: data
  })
  return (await res).data
}
