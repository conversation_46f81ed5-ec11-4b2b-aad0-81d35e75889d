import request from '_/utils/request'
import { InvoiceDetailReq, InvoiceDetailRes, UploadInvoiceReq, UploadInvoiceRes } from './types/costInvoiceManage'

/**
 * @description: 获取发票详情信息
 * @param {InvoiceDetailReq} data - 请求体
 * @returns { Promise<InvoiceDetailRes> } - 返回体
 */
const getInvoiceDetail = async (data: InvoiceDetailReq): Promise<InvoiceDetailRes> => {
  const res = await request.post('/zjpt-boss/bankCost/invoice/getAndValidInvoiceInfos', data)
  return (await res).data
}

const uploadInvoice = async (data: UploadInvoiceReq): Promise<UploadInvoiceRes> => {
  const res = await request.post('/zjpt-boss/bankCost/invoice/importCostInvoice', data)
  return (await res).data
}

export {
    getInvoiceDetail,
    uploadInvoice
}
