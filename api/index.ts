import request from '_/utils/request'
import { EnumsReq, EnumsRes } from './types/enums'

/**
 * @description 上传文件
 */
const uploadFile = async (data: any): Promise<any> => {
  const res = await request.post('/zjpt-boss/main/uploadFile', data)
  return (await res).data
}

/**
 * @description 备付金报备枚举
 */
const bankReportEnums = async (data?: EnumsReq): Promise<EnumsRes> => {
  const res = await request.post('/bankreport-server/common/query/enum', data || {})
  return res.data
}

/**
 * @description 成本计费枚举
 */
const costEnums = async (data?: EnumsReq): Promise<EnumsRes> => {
  const res = await request.post('/cost-manage-server/common/getEnums', data || {})
  return res.data
}

/**
 * @description 获取计费元素映射枚举
 */
const getCalculateItemEnum = async (): Promise<EnumsRes> => {
  const res = await request.post('/cost-manage-server/common/getCalculateItemEnum')
  return res.data
}

export {
  uploadFile,
  bankReportEnums,
  costEnums,
  getCalculateItemEnum
}
