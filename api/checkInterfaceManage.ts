import request from '_/utils/request'

import { ConfigInfoRes } from '_/api/types/checkInterfaceManage'
import { ConfigRule } from '_/types/interFace'
import { ApiBaseRes } from '_/api/types'
// import { CostInvoiceType } from '_/types/invoiceManage'
// import { ConfigInfoReq } from './types/costInvoiceManage'
/**
 * @description: 获取配置信息
 * @param data { ConfigCode: string } 配置code
 * @returns { Promise<ConfigInfoRes> } 配置信息
 */
const getConfigInfoApi = async (data: { configCode: string }): Promise<ConfigInfoRes> => {
  const res = await request.post('/check-center-hessian/checkCenter/checkConfig/InfoRuleByConfigCode', data)
  return (await res).data
}

/**
 *  @description: 保存规则
 */
const saveRuleApi = async (data: { configCode: string, rulelVos: ConfigRule[] }): Promise<ApiBaseRes> => {
  const res = await request.post('/check-center-hessian/checkCenter/checkConfig/createAndUpdateRuleByConfigCode', data)
  return (await res).data
}

// const getCostInvoiceList = async (data: ConfigInfoReq): Promise<CostInvoiceType[]> => {
//   const res = await request.post('/check-center-hessian/bankCost/invoice/queryCostInvoicePage', data)
//   return (await res).data
// }
// 下单接口
// export type SinglePayParam = {
//   /** 商户订单号 */
//   requestNo?: string
//   /** 订单金额 */
//   orderAmount: number | undefined
//   /** 收款卡号 */
//   receiverAccountNo: string | undefined
//   /** 收款方名称 */
//   receiverAccountName: string | undefined
//   /** 收款银行编码 */
//   receiverBankCode: string | undefined
//   /** 省编码 */
//   province?: string
//   /** 市编码 */
//   city?: string
//   /** 支行编码 */
//   branchBankCode?: string
//   /** 银行附言 */
//   comments?: string
//   /** 到账时效 */
//   receiveType: string
//   /** 订单备注 */
//   orderInfo?: string
//   /** 短信验证码 */
//   smsCode: string
//   /** 交易密码 */
//   passwd: string
//   /** 产品类型 */
//   productType: string
// }
// type SinglePayRes = ApiRes.Common & {
//   data: {
//     /** 请求号 */
//     requestNo: string
//     /** 易宝流水号 */
//     orderNo: string
//     /** REQUEST_RECEIVE 等待复核,其他均是已受理. REQUEST_ACCEPT 已接受理,REMITING银行处理中,SUCCESS成功(不会出现),FAIL 失败(不会出现) */
//     status: string
//     /** 付款时间 */
//     orderTime: string
//   }
// }
// const SinglePay = (data: SinglePayParam): Promise<SinglePayRes> => request.post('/remit/single', data)
export {
  getConfigInfoApi,
  saveRuleApi
}
