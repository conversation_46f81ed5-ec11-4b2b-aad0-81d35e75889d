import request from '_/utils/request'
type billCheckBatchReq = {
}
type billCheckBatchRes = {

}

/**
 * @description: 批量生成付款单预校验
 * @param {billCheckBatchReq} data - 请求体
 * @returns { Promise<billCheckBatchRes> } - 返回体
 */
const billCheckBatch = async (data: billCheckBatchReq): Promise<billCheckBatchRes> => {
  const res = await request.post('/finance-process-hessian/financeProcess/payment/bill/checkBatch', data)
  return (await res).data
}

export {
  billCheckBatch
}
