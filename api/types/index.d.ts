declare namespace ApiRes {
    interface Common {
        data: unknown
        code: number | string
        message: string
    }
}
declare namespace ApiReq {
    interface Paging {
        page: number,
        size: number,
    }
}
export type ApiBaseRes = ApiRes.Common

// 文件上传响应结构体
export type ConfigInfoRes = ApiRes.Common & {
    data: {
      respVo: object,
      detailVoList: object,
      rulelVos: object[],
      alarmlVo: object,
    }
}
