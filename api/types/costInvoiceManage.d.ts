import { ApiReq, ApiRes } from './index'
/**
 * @description 成本发票请求体
 * @property {string} bankCode - 收款方编码
 * @property {string} paymentMode - 支付方式
 * @property {string} orderNo - 付款单号
 * @property {string} billNo - 账单单号
 * @property {string} invoiceNo - 发票号
 * @property {string} status - 发票状态
 */
export type ConfigInfoReq = ApiReq.Paging & {
  /** 收款方编码 */
  bankCode: string
  /** 支付方式 */
  paymentMode: string
  /** 付款单号 */
  orderNo: string
  /** 账单单号 */
  billNo: string
  /** 发票号 */
  invoiceNo: string
  /** 发票状态 */
  status: string
}

/**
 * @description OSS文件信息
 * @property {string} fileName - 文件名
 * @property {string} fileType - 文件类型
 * @property {string} fileUrl - 发票云存储地址
 */
export type InvoiceOSSInfo = {
  /** 文件名 */
  fileName: string
  /** 文件类型 */
  fileType: string
  /** 发票云存储地址 */
  fileUrl: string
}
/**
 * @description 获取发票详情请求体
 */
export type InvoiceDetailReq = {
  uploadFileVos: InvoiceOSSInfo[]
}

/**
 * @description 发票详情
 * @property {string} invoiceNo - 发票号
 * @property {number} realInvoiceAmount - 实际发票金额
 * @property {number} realTaxAmount - 实际发票税额
 * @property {number} usedInvoiceAmount - 已使用发票金额
 * @property {number} usedTaxAmount - 已使用发票税额
 */
export type InvoiceDetail = {
  /** 发票号 */
  realInvoiceNo: string
  /** 实际发票金额 */
  realInvoiceAmount: number
  /** 实际发票税额 */
  realTaxAmount: number
  /** 已使用发票金额 */
  usedInvoiceAmount: number
  /** 已使用发票税额 */
  usedTaxAmount: number
  /** 剩余发票金额 */
  remainInvoiceAmount: number
  /** 剩余发票税额 */
  remainTaxAmount: number
  /** 默认数据 */
  defaultItem: object
}
/**
 * @description 发票详情返回体
 * @property {InvoiceDetail} data - 发票详情
 */
export type InvoiceDetailRes = ApiRes.Common & {
  data: InvoiceDetail[]
}

// -------------------------------上传发票-----------------

/**
 * @description 使用信息
 */
export type UseInvoiceType = {
  realInvoiceNo: string
  invoiceAmount: number
  taxAmount: number
  invoiceInfo: string
  remark?: string
}

export type CostLinkInvoiceType = {
  /** 发票号 */
  invoiceNo: string
  /** 使用发票列表 */
  details: UseInvoiceType[]
}

/**
 * @description 上传发票请求体
 */
export type UploadInvoiceReq = {
  /** 发票详情 */
  invoices: CostLinkInvoiceType[]
}

export type UploadInvoiceRes = ApiRes.Common
