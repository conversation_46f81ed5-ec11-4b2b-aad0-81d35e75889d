import { ApiReq, ApiRes } from '_/api/types/index'
import { CommonRule, CalFeeRuleDetail, RouteItem, CalculatePrecedenceBaseInfo, CalFeeRule, CalFeeRuleDetailNormal, CalElementDetail, CheckCodesAndItService } from '_/types/costCharging'
// 计费优先级请求体
export interface CalculatePrecedenceReq {
  calculatePrecedences: string[]
}
// 计费优先级响应体
export type CalculatePrecedenceRes = ApiRes.Common & {
  data: CalculatePrecedenceBaseInfo[]
}

// 计费规则详情请求体
export interface CalRuleDetailReq {
  routeId: number
}
// 计费规则详情响应体
export type CalRuleDetailRes = ApiRes.Common & {
  data: CalFeeRuleDetail
}

// 修改通用计费规则请求体
export interface UpdateCommonRuleReq {
  ruleRouteId: number
  ruleRouteName: string
  calculateType: string
  calRuleDetailVos: CalFeeRule[]
}
// 修改通用计费规则响应体
export type UpdateCommonRuleRes = ApiRes.Common

// 查询通用计费规则列表
export interface QueryCommonRuleListReq {

}
// 查询通用计费规则列表 响应体
export type QueryCommonRuleListRes = ApiRes.Common & {
  data: CommonRule[]
}

// -----------------------普通计费规则-----------------------

// 查询详情 /ruleRoute/addRuleRouteList
export interface QueryRuleRouteListReq {
  // 路由分组编号
  groupNo: string
}
// 查询详情 响应体
export type QueryRuleRouteListRes = ApiRes.Common & {
  data: RouteItem[]
}

// 根据路由id获取计费规则列表
export interface QueryCalRulesByRouteIdReq {
  calRouteId: number
}
// 根据路由id获取计费规则列表 响应体
export type QueryCalRulesByRouteIdRes = ApiRes.Common & {
  data: CalFeeRule[]
}

// 根据计算规则id查询计费规则列表
export interface QueryCalRuleByRuleIdReq {
  ruleId: number
}
// 根据计算规则id查询计费规则列表 响应体
export type QueryCalRuleByRuleIdRes = ApiRes.Common & {
  data: CalFeeRuleDetailNormal
}

// 新增/修改计费优先级请求体
export type OperateRuleRouteReq = RouteItem

// 新增/修改计费优先级响应体
export type OperateRuleRouteRes = ApiRes.Common

// 新增/修改计费规则请求体
export type OperateCalRuleReq = {
  // 计算规则id-修改需要
  id?: number
  // 路由id
  routeId: number
  // 工单号
  ticketNo: string
  // 工单名称
  ticketName: string
  // 计费类型(无阶梯、有阶梯)
  calculateType: string
  // 生效开始时间
  validityStartTime: string
  // 生效结束时间
  validityEndTime: string
  // 累计开始时间
  accumulationStartTime: string
  // 累计结束时间
  accumulationEndTime: string
  // 计费规则列表
  calRules: CalFeeRule[]
}
// 新增/修改计费规则响应体
export type OperateCalRuleRes = ApiRes.Common

// 计费元素规则组
export interface QueryCalEleGroupReq {
  // 计费元素数组
  calculatePrecedences: string[]
}
// 计费元素规则组 响应体
export type QueryCalEleGroupRes = ApiRes.Common & {
  data: CalEleGroup[]
}

// 获取配置了结算方式的对账接口/技术服务商
export interface QueryCheckCodesAndItServiceReq {
  // 清算机构
  clearingOrgCode?: string,
  // 状态
  status?: string
}
// 获取配置了结算方式的对账接口/技术服务商 响应体
export type QueryCheckCodesAndItServiceRes = ApiRes.Common & {
  data: CheckCodesAndItService[]
}
