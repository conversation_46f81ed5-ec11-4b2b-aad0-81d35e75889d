import { ApiReq, ApiRes } from '_/api/types/index'
import { ReportRulerItem } from '_/types/excessReserveManage/reportCheckRuler'
import { AdjustRecord, CalcProcess } from '_/types/excessReserveManage/reportDetails'
// 报备详情
export type ReportDetailReq = {
  taskId: number | string
}
export type ReportDetailRes = ApiRes.Common & {
  data: {
    // 清算机构
    reportType: string,
    // 记账日期
    accountsDate: string,
    // 状态
    status: string,
    [key: string]: any
  }
}
// 调账
export type AdjustReq = {
  taskId: number | string,
  symbol: '+' | '-',
  amount: string,
  bizType: string,
  reportFileType: string,
  reportTableField: string,
  remark: string
}
export type AdjustRes = ApiRes.Common

// 现金净流量看板
export type CashFlowBoardReq = {
  taskId: number | string
}
export type CashFlowBoardRes = ApiRes.Common & {
  data: {
    // 现金净流量
    cashFlow: number,
    // 报备金额
    reportAmount: number,
    // 是否相等
    isEqual: boolean,
    [key: string]: any
  }
}
// 核验失败规则
export type CheckFailRulesReq = {
  taskId: number | string
}
export type CheckFailRulesRes = ApiRes.Common & {
  data: ReportRulerItem[]
}
// 计算过程详情
export type CalcProcessReq = {
  /** 清算机构 */
  reportType: string,
  /** 记账时间 */
  accountsDate: string,
  /** 报表 */
  reportTable: string,
  /** 报表字段 */
  reportField: string,
}
export type CalcProcessRes = ApiRes.Common & {
  data: CalcProcess[]
}
// 调账记录列表
export type AdjustRecordsReq = {
  /** 任务id */
  taskId: number | string,
  /** 报表字段 */
  reportTableField: string,
  /** 报表名 */
  reportFileType: string,
}
export type AdjustRecordsRes = ApiRes.Common & {
  data: AdjustRecord[]
}
