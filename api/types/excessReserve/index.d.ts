type RulerItem = {
  /** 规则明细id */
  id?: number,
  /** 规则主表id */
  ruleId?: number,
  /** 规则符号 */
  symbol: '+' | '-' | '',
  /** 报表x */
  reportTable: string,
  /** x报表字段 */
  reportField: string,
  /** 规则明细左右位置 */
  checkPosition: string,
  remark?: string,
}

export type OperateRulerReq = {
  id?: number,
  checkMethod: '=',
  /** 清算机构 */
  clearOrgBank: string,
  /** 启用状态 */
  status: string,
  detailInfoReqVoList: RulerItem[],
  remark?: string,
}
