import request from '_/utils/request'
import { ApiBaseRes } from '_/api/types/index'
import { OperateRulerReq } from '_/api/types/excessReserve'
import { AdjustRecordsReq, AdjustRecordsRes, AdjustReq, AdjustRes, CalcProcessReq, CalcProcessRes, CashFlowBoardReq, CashFlowBoardRes, CheckFailRulesReq, CheckFailRulesRes, ReportDetailReq, ReportDetailRes } from '_/api/types/excessReserve/reportDetail'
/**
 * @description 操作规则
 */
const operaRulerApi = async (data: OperateRulerReq): Promise<ApiBaseRes> => {
  const res = await request.post('/bankreport-server/bankReport/rule/createOrUpdateRule', data)
  return (await res).data
}

/**
 * @description 报备详情
 */
const reportDetailApi = async (data: ReportDetailReq): Promise<ReportDetailRes> => {
  const res = await request.get('/bankreport-server/reportData/queryReportData', {
    params: data
  })
  return (await res).data
}

/**
 * @description 调账
 */
const adjustApi = async (data: AdjustReq): Promise<AdjustRes> => {
  const res = await request.post('/bankreport-server/reportData/adjustment', data)
  return (await res).data
}

/**
 * @description 获取净流量数据
 */
const cashFlowBoardApi = async (data: CashFlowBoardReq): Promise<CashFlowBoardRes> => {
  const res = await request.get('/bankreport-server/reportData/queryCashFlowBoard', {
    params: data
  })
  return (await res).data
}
/**
 * @description 获取核验失败公式
 * /bankReport/rule/detailInfoByTaskId
 */
const getCheckFailRules = async (data: CheckFailRulesReq): Promise<CheckFailRulesRes> => {
  const res = await request.post('/bankreport-server/bankReport/rule/detailInfoByTaskId', data)
  return (await res).data
}
/**
 * @description 获取计算过程详情
 */
const getCalcProcess = async (data: CalcProcessReq): Promise<CalcProcessRes> => {
  const res = await request.post('/bankreport-server/common/query/fieldInfos', data)
  return (await res).data
}
/**
 * @description 获取调账记录列表
 */
const getAdjustRecords = async (data: AdjustRecordsReq): Promise<AdjustRecordsRes> => {
  const res = await request.post('/bankreport-server/reportData/queryAdjustment', data)
  return (await res).data
}
export {
  operaRulerApi,
  reportDetailApi,
  adjustApi,
  cashFlowBoardApi,
  getCheckFailRules,
  getCalcProcess,
  getAdjustRecords
}
