<template>
  <div class="te_container">
    <a-table :columns="columns" :data-source="dataSource" :size="size" :pagination="false" :rowKey="'id'">
      <template slot="footer">
        <div>
          <a-button type="dashed" :block="true" icon="plus" style="opacity: 0.8" @click="xxx">
            新增
          </a-button>
        </div>
      </template>
    </a-table>
  </div>
</template>

<script lang="ts">
import { defineComponent } from '@vue/composition-api'
export default defineComponent({
  name: 'HTableEdit',
  props: {
    columns: {
      type: Array,
      required: true
    },
    dataSource: {
      type: Array,
      required: true
    },
    size: {
      type: String,
      default: 'small',
      validator(value: string) {
        // 枚举值
        return ['small', 'middle', 'default'].includes(value)
      }
    },
    rowKey: {
      type: String,
      required: true,
      default: 'id'
    }
  },

  data() {
    return {}
  },

  mounted() {},

  methods: {}
})
</script>
