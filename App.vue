<template>
  <div id="app">
    <a-config-provider :locale="locale">
      <router-view />
    </a-config-provider>
  </div>
</template>

<script>
import zhCN from 'ant-design-vue/es/locale/zh_CN'
import { config } from '@yeepay/lowcode-renderer' // 配置enums函数
import { fetchEnum } from '_/utils/fetchEnums'
export default {
  name: 'App',
  data() {
    return {
      locale: zhCN,
      loadEnum: false
    }
  },
  created() {
    this.loadEnum = true
    fetchEnum({
        qaCode: 'fundAccountingOperation', // qa项目code
        code: 'fundAccountingOperation' // 生产项目code
    }).then(enums => {
        config({ enums })
        this.loadEnum = false
    }).catch(() => {
        this.loadEnum = false
    })
 }
}
</script>
